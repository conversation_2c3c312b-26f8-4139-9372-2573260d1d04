{"name": "gaming-lounge-frontend", "version": "1.0.0", "description": "Frontend React app for Gaming Lounge Reservation System", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.15.0", "socket.io-client": "^4.7.2", "axios": "^1.5.0", "react-calendar": "^4.6.0", "react-time-picker": "^6.5.0", "styled-components": "^6.0.7", "framer-motion": "^10.16.4", "react-hot-toast": "^2.4.1", "lucide-react": "^0.279.0", "date-fns": "^2.30.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0"}, "proxy": "http://localhost:5000"}