{"ast": null, "code": "import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nexport class Polling extends Transport {\n  constructor() {\n    super(...arguments);\n    this._polling = false;\n  }\n  get name() {\n    return \"polling\";\n  }\n  /**\n   * Opens the socket (triggers polling). We write a PING message to determine\n   * when the transport is open.\n   *\n   * @protected\n   */\n  doOpen() {\n    this._poll();\n  }\n  /**\n   * Pauses polling.\n   *\n   * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n   * @package\n   */\n  pause(onPause) {\n    this.readyState = \"pausing\";\n    const pause = () => {\n      this.readyState = \"paused\";\n      onPause();\n    };\n    if (this._polling || !this.writable) {\n      let total = 0;\n      if (this._polling) {\n        total++;\n        this.once(\"pollComplete\", function () {\n          --total || pause();\n        });\n      }\n      if (!this.writable) {\n        total++;\n        this.once(\"drain\", function () {\n          --total || pause();\n        });\n      }\n    } else {\n      pause();\n    }\n  }\n  /**\n   * Starts polling cycle.\n   *\n   * @private\n   */\n  _poll() {\n    this._polling = true;\n    this.doPoll();\n    this.emitReserved(\"poll\");\n  }\n  /**\n   * Overloads onData to detect payloads.\n   *\n   * @protected\n   */\n  onData(data) {\n    const callback = packet => {\n      // if its the first message we consider the transport open\n      if (\"opening\" === this.readyState && packet.type === \"open\") {\n        this.onOpen();\n      }\n      // if its a close packet, we close the ongoing requests\n      if (\"close\" === packet.type) {\n        this.onClose({\n          description: \"transport closed by the server\"\n        });\n        return false;\n      }\n      // otherwise bypass onData and handle the message\n      this.onPacket(packet);\n    };\n    // decode payload\n    decodePayload(data, this.socket.binaryType).forEach(callback);\n    // if an event did not trigger closing\n    if (\"closed\" !== this.readyState) {\n      // if we got data we're not polling\n      this._polling = false;\n      this.emitReserved(\"pollComplete\");\n      if (\"open\" === this.readyState) {\n        this._poll();\n      } else {}\n    }\n  }\n  /**\n   * For polling, send a close packet.\n   *\n   * @protected\n   */\n  doClose() {\n    const close = () => {\n      this.write([{\n        type: \"close\"\n      }]);\n    };\n    if (\"open\" === this.readyState) {\n      close();\n    } else {\n      // in case we're trying to close while\n      // handshaking is in progress (GH-164)\n      this.once(\"open\", close);\n    }\n  }\n  /**\n   * Writes a packets payload.\n   *\n   * @param {Array} packets - data packets\n   * @protected\n   */\n  write(packets) {\n    this.writable = false;\n    encodePayload(packets, data => {\n      this.doWrite(data, () => {\n        this.writable = true;\n        this.emitReserved(\"drain\");\n      });\n    });\n  }\n  /**\n   * Generates uri for connection.\n   *\n   * @private\n   */\n  uri() {\n    const schema = this.opts.secure ? \"https\" : \"http\";\n    const query = this.query || {};\n    // cache busting is forced\n    if (false !== this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = randomString();\n    }\n    if (!this.supportsBinary && !query.sid) {\n      query.b64 = 1;\n    }\n    return this.createUri(schema, query);\n  }\n}", "map": {"version": 3, "names": ["Transport", "randomString", "encodePayload", "decodePayload", "Polling", "constructor", "arguments", "_polling", "name", "doOpen", "_poll", "pause", "onPause", "readyState", "writable", "total", "once", "doPoll", "emit<PERSON><PERSON><PERSON><PERSON>", "onData", "data", "callback", "packet", "type", "onOpen", "onClose", "description", "onPacket", "socket", "binaryType", "for<PERSON>ach", "doClose", "close", "write", "packets", "doWrite", "uri", "schema", "opts", "secure", "query", "timestampRequests", "timestampParam", "supportsBinary", "sid", "b64", "createUri"], "sources": ["C:/Users/<USER>/Desktop/hamza/frontend/node_modules/engine.io-client/build/esm/transports/polling.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nexport class Polling extends Transport {\n    constructor() {\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    _poll() {\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,YAAY,QAAQ,YAAY;AACzC,SAASC,aAAa,EAAEC,aAAa,QAAQ,kBAAkB;AAC/D,OAAO,MAAMC,OAAO,SAASJ,SAAS,CAAC;EACnCK,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACzB;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACC,KAAK,CAAC,CAAC;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,KAAKA,CAACC,OAAO,EAAE;IACX,IAAI,CAACC,UAAU,GAAG,SAAS;IAC3B,MAAMF,KAAK,GAAGA,CAAA,KAAM;MAChB,IAAI,CAACE,UAAU,GAAG,QAAQ;MAC1BD,OAAO,CAAC,CAAC;IACb,CAAC;IACD,IAAI,IAAI,CAACL,QAAQ,IAAI,CAAC,IAAI,CAACO,QAAQ,EAAE;MACjC,IAAIC,KAAK,GAAG,CAAC;MACb,IAAI,IAAI,CAACR,QAAQ,EAAE;QACfQ,KAAK,EAAE;QACP,IAAI,CAACC,IAAI,CAAC,cAAc,EAAE,YAAY;UAClC,EAAED,KAAK,IAAIJ,KAAK,CAAC,CAAC;QACtB,CAAC,CAAC;MACN;MACA,IAAI,CAAC,IAAI,CAACG,QAAQ,EAAE;QAChBC,KAAK,EAAE;QACP,IAAI,CAACC,IAAI,CAAC,OAAO,EAAE,YAAY;UAC3B,EAAED,KAAK,IAAIJ,KAAK,CAAC,CAAC;QACtB,CAAC,CAAC;MACN;IACJ,CAAC,MACI;MACDA,KAAK,CAAC,CAAC;IACX;EACJ;EACA;AACJ;AACA;AACA;AACA;EACID,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACH,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACU,MAAM,CAAC,CAAC;IACb,IAAI,CAACC,YAAY,CAAC,MAAM,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIC,MAAMA,CAACC,IAAI,EAAE;IACT,MAAMC,QAAQ,GAAIC,MAAM,IAAK;MACzB;MACA,IAAI,SAAS,KAAK,IAAI,CAACT,UAAU,IAAIS,MAAM,CAACC,IAAI,KAAK,MAAM,EAAE;QACzD,IAAI,CAACC,MAAM,CAAC,CAAC;MACjB;MACA;MACA,IAAI,OAAO,KAAKF,MAAM,CAACC,IAAI,EAAE;QACzB,IAAI,CAACE,OAAO,CAAC;UAAEC,WAAW,EAAE;QAAiC,CAAC,CAAC;QAC/D,OAAO,KAAK;MAChB;MACA;MACA,IAAI,CAACC,QAAQ,CAACL,MAAM,CAAC;IACzB,CAAC;IACD;IACAnB,aAAa,CAACiB,IAAI,EAAE,IAAI,CAACQ,MAAM,CAACC,UAAU,CAAC,CAACC,OAAO,CAACT,QAAQ,CAAC;IAC7D;IACA,IAAI,QAAQ,KAAK,IAAI,CAACR,UAAU,EAAE;MAC9B;MACA,IAAI,CAACN,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACW,YAAY,CAAC,cAAc,CAAC;MACjC,IAAI,MAAM,KAAK,IAAI,CAACL,UAAU,EAAE;QAC5B,IAAI,CAACH,KAAK,CAAC,CAAC;MAChB,CAAC,MACI,CACL;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIqB,OAAOA,CAAA,EAAG;IACN,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAChB,IAAI,CAACC,KAAK,CAAC,CAAC;QAAEV,IAAI,EAAE;MAAQ,CAAC,CAAC,CAAC;IACnC,CAAC;IACD,IAAI,MAAM,KAAK,IAAI,CAACV,UAAU,EAAE;MAC5BmB,KAAK,CAAC,CAAC;IACX,CAAC,MACI;MACD;MACA;MACA,IAAI,CAAChB,IAAI,CAAC,MAAM,EAAEgB,KAAK,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,KAAKA,CAACC,OAAO,EAAE;IACX,IAAI,CAACpB,QAAQ,GAAG,KAAK;IACrBZ,aAAa,CAACgC,OAAO,EAAGd,IAAI,IAAK;MAC7B,IAAI,CAACe,OAAO,CAACf,IAAI,EAAE,MAAM;QACrB,IAAI,CAACN,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACI,YAAY,CAAC,OAAO,CAAC;MAC9B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIkB,GAAGA,CAAA,EAAG;IACF,MAAMC,MAAM,GAAG,IAAI,CAACC,IAAI,CAACC,MAAM,GAAG,OAAO,GAAG,MAAM;IAClD,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC;IAC9B;IACA,IAAI,KAAK,KAAK,IAAI,CAACF,IAAI,CAACG,iBAAiB,EAAE;MACvCD,KAAK,CAAC,IAAI,CAACF,IAAI,CAACI,cAAc,CAAC,GAAGzC,YAAY,CAAC,CAAC;IACpD;IACA,IAAI,CAAC,IAAI,CAAC0C,cAAc,IAAI,CAACH,KAAK,CAACI,GAAG,EAAE;MACpCJ,KAAK,CAACK,GAAG,GAAG,CAAC;IACjB;IACA,OAAO,IAAI,CAACC,SAAS,CAACT,MAAM,EAAEG,KAAK,CAAC;EACxC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}