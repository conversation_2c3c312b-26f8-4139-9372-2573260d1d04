<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberZone Gaming Lounge - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem 0;
        }

        .title {
            font-size: 4rem;
            font-weight: 900;
            background: linear-gradient(45deg, #00ffff, #8a2be2, #00ff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
        }

        .subtitle {
            font-size: 1.5rem;
            color: #b0b0b0;
            margin-bottom: 2rem;
        }

        .pc-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .pc-card {
            background: rgba(26, 26, 46, 0.4);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 255, 255, 0.2);
            border-radius: 16px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .pc-card:hover {
            transform: translateY(-5px);
            border-color: #00ffff;
            box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
        }

        .pc-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(45deg, #00ff00, #00cc00);
        }

        .pc-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #00ffff;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
            margin-bottom: 0.5rem;
        }

        .pc-name {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .pc-specs {
            color: #b0b0b0;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .status-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            background: linear-gradient(45deg, #00ff00, #00cc00);
            color: white;
            text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
        }

        .reserve-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #00ffff, #8a2be2);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 1rem;
        }

        .reserve-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 255, 0.4);
        }

        .info-box {
            background: rgba(26, 26, 46, 0.4);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 255, 255, 0.2);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .demo-note {
            color: #ffff00;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .setup-instructions {
            color: #b0b0b0;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2.5rem;
            }
            .container {
                padding: 1rem;
            }
            .pc-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">CYBERZONE GAMING LOUNGE</h1>
            <p class="subtitle">Experience gaming like never before with our high-performance gaming rigs</p>
        </div>

        <div class="info-box">
            <div class="demo-note">🎮 DEMO VERSION - Static Preview</div>
            <div class="setup-instructions">
                This is a static preview of the Gaming Lounge interface.<br>
                To run the full application with real-time features, backend API, and database:<br>
                1. Set up MySQL database<br>
                2. Start the backend server (Node.js/Express)<br>
                3. Start the frontend server (React.js)<br>
                <br>
                See setup.md for detailed instructions.
            </div>
        </div>

        <div class="pc-grid" id="pcGrid">
            <!-- PCs will be generated by JavaScript -->
        </div>
    </div>

    <script>
        // Generate PC cards
        const pcGrid = document.getElementById('pcGrid');
        
        for (let i = 1; i <= 12; i++) {
            const status = i <= 8 ? 'available' : (i <= 10 ? 'in_use' : 'maintenance');
            const statusColor = status === 'available' ? '#00ff00' : 
                               status === 'in_use' ? '#ff6600' : '#ff0040';
            
            const pcCard = document.createElement('div');
            pcCard.className = 'pc-card';
            pcCard.innerHTML = `
                <div class="status-badge" style="background: linear-gradient(45deg, ${statusColor}, ${statusColor}aa)">
                    ${status.replace('_', ' ')}
                </div>
                <div class="pc-number">PC ${i}</div>
                <div class="pc-name">Gaming Beast ${i.toString().padStart(2, '0')}</div>
                <div class="pc-specs">
                    🖥️ Intel i9-13900K<br>
                    🎮 RTX 4080<br>
                    💾 32GB DDR5<br>
                    💿 2TB NVMe SSD
                </div>
                <button class="reserve-btn" ${status !== 'available' ? 'disabled' : ''}>
                    ${status === 'available' ? 'Reserve Now' : 
                      status === 'in_use' ? 'Currently In Use' : 'Under Maintenance'}
                </button>
            `;
            
            pcGrid.appendChild(pcCard);
        }

        // Add some interactivity
        document.querySelectorAll('.reserve-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (!this.disabled) {
                    alert('🎮 Reservation feature requires the full application setup!\n\nTo enable reservations:\n1. Set up the backend server\n2. Configure the database\n3. Run the React frontend');
                }
            });
        });

        // Add hover sound effect simulation
        document.querySelectorAll('.pc-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                // Simulate hover effect
                this.style.transform = 'translateY(-5px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
