{"ast": null, "code": "import { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { camelToDash } from '../dom/utils/camel-to-dash.mjs';\nimport { camelCaseAttributes } from './utils/camel-case-attrs.mjs';\nimport { transformProps } from '../html/utils/transform.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { getDefaultValueType } from '../dom/value-types/defaults.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nclass SVGVisualElement extends DOMVisualElement {\n  constructor() {\n    super(...arguments);\n    this.type = \"svg\";\n    this.isSVGTag = false;\n  }\n  getBaseTargetFromProps(props, key) {\n    return props[key];\n  }\n  readValueFromInstance(instance, key) {\n    if (transformProps.has(key)) {\n      const defaultType = getDefaultValueType(key);\n      return defaultType ? defaultType.default || 0 : 0;\n    }\n    key = !camelCaseAttributes.has(key) ? camelToDash(key) : key;\n    return instance.getAttribute(key);\n  }\n  measureInstanceViewportBox() {\n    return createBox();\n  }\n  scrapeMotionValuesFromProps(props, prevProps) {\n    return scrapeMotionValuesFromProps(props, prevProps);\n  }\n  build(renderState, latestValues, options, props) {\n    buildSVGAttrs(renderState, latestValues, options, this.isSVGTag, props.transformTemplate);\n  }\n  renderInstance(instance, renderState, styleProp, projection) {\n    renderSVG(instance, renderState, styleProp, projection);\n  }\n  mount(instance) {\n    this.isSVGTag = isSVGTag(instance.tagName);\n    super.mount(instance);\n  }\n}\nexport { SVGVisualElement };", "map": {"version": 3, "names": ["scrapeMotionValuesFromProps", "DOMVisualElement", "buildSVGAttrs", "camelToDash", "camelCaseAttributes", "transformProps", "renderSVG", "getDefaultValueType", "createBox", "isSVGTag", "SVGVisualElement", "constructor", "arguments", "type", "getBaseTargetFromProps", "props", "key", "readValueFromInstance", "instance", "has", "defaultType", "default", "getAttribute", "measureInstanceViewportBox", "prevProps", "build", "renderState", "latestValues", "options", "transformTemplate", "renderInstance", "styleProp", "projection", "mount", "tagName"], "sources": ["C:/Users/<USER>/Desktop/hamza/frontend/node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs"], "sourcesContent": ["import { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { camelToDash } from '../dom/utils/camel-to-dash.mjs';\nimport { camelCaseAttributes } from './utils/camel-case-attrs.mjs';\nimport { transformProps } from '../html/utils/transform.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { getDefaultValueType } from '../dom/value-types/defaults.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\n\nclass SVGVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"svg\";\n        this.isSVGTag = false;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props[key];\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            const defaultType = getDefaultValueType(key);\n            return defaultType ? defaultType.default || 0 : 0;\n        }\n        key = !camelCaseAttributes.has(key) ? camelToDash(key) : key;\n        return instance.getAttribute(key);\n    }\n    measureInstanceViewportBox() {\n        return createBox();\n    }\n    scrapeMotionValuesFromProps(props, prevProps) {\n        return scrapeMotionValuesFromProps(props, prevProps);\n    }\n    build(renderState, latestValues, options, props) {\n        buildSVGAttrs(renderState, latestValues, options, this.isSVGTag, props.transformTemplate);\n    }\n    renderInstance(instance, renderState, styleProp, projection) {\n        renderSVG(instance, renderState, styleProp, projection);\n    }\n    mount(instance) {\n        this.isSVGTag = isSVGTag(instance.tagName);\n        super.mount(instance);\n    }\n}\n\nexport { SVGVisualElement };\n"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,QAAQ,QAAQ,wBAAwB;AAEjD,MAAMC,gBAAgB,SAAST,gBAAgB,CAAC;EAC5CU,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAACJ,QAAQ,GAAG,KAAK;EACzB;EACAK,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/B,OAAOD,KAAK,CAACC,GAAG,CAAC;EACrB;EACAC,qBAAqBA,CAACC,QAAQ,EAAEF,GAAG,EAAE;IACjC,IAAIX,cAAc,CAACc,GAAG,CAACH,GAAG,CAAC,EAAE;MACzB,MAAMI,WAAW,GAAGb,mBAAmB,CAACS,GAAG,CAAC;MAC5C,OAAOI,WAAW,GAAGA,WAAW,CAACC,OAAO,IAAI,CAAC,GAAG,CAAC;IACrD;IACAL,GAAG,GAAG,CAACZ,mBAAmB,CAACe,GAAG,CAACH,GAAG,CAAC,GAAGb,WAAW,CAACa,GAAG,CAAC,GAAGA,GAAG;IAC5D,OAAOE,QAAQ,CAACI,YAAY,CAACN,GAAG,CAAC;EACrC;EACAO,0BAA0BA,CAAA,EAAG;IACzB,OAAOf,SAAS,CAAC,CAAC;EACtB;EACAR,2BAA2BA,CAACe,KAAK,EAAES,SAAS,EAAE;IAC1C,OAAOxB,2BAA2B,CAACe,KAAK,EAAES,SAAS,CAAC;EACxD;EACAC,KAAKA,CAACC,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEb,KAAK,EAAE;IAC7Cb,aAAa,CAACwB,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAE,IAAI,CAACnB,QAAQ,EAAEM,KAAK,CAACc,iBAAiB,CAAC;EAC7F;EACAC,cAAcA,CAACZ,QAAQ,EAAEQ,WAAW,EAAEK,SAAS,EAAEC,UAAU,EAAE;IACzD1B,SAAS,CAACY,QAAQ,EAAEQ,WAAW,EAAEK,SAAS,EAAEC,UAAU,CAAC;EAC3D;EACAC,KAAKA,CAACf,QAAQ,EAAE;IACZ,IAAI,CAACT,QAAQ,GAAGA,QAAQ,CAACS,QAAQ,CAACgB,OAAO,CAAC;IAC1C,KAAK,CAACD,KAAK,CAACf,QAAQ,CAAC;EACzB;AACJ;AAEA,SAASR,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}