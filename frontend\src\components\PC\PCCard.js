import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { Monitor, Cpu, HardDrive, Zap } from 'lucide-react';

const CardContainer = styled(motion.div)`
  background: rgba(26, 26, 46, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-5px);
    border-color: ${props => getStatusColor(props.status)};
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${props => getStatusGradient(props.status)};
  }

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, ${props => getStatusColor(props.status)}20 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::after {
    opacity: 1;
  }
`;

const PCHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const PCNumber = styled.div`
  font-family: 'Orbitron', monospace;
  font-size: 1.5rem;
  font-weight: 700;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
`;

const StatusBadge = styled.div`
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  background: ${props => getStatusGradient(props.status)};
  color: white;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  animation: ${props => props.status === 'available' ? 'pulse-glow 2s infinite' : 'none'};
`;

const PCName = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #ffffff;
`;

const SpecsList = styled.div`
  display: grid;
  gap: 0.8rem;
  margin-bottom: 1.5rem;
`;

const SpecItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.8rem;
  color: #b0b0b0;
  font-size: 0.9rem;

  .icon {
    color: #00ffff;
    width: 16px;
    height: 16px;
  }

  .label {
    font-weight: 600;
    min-width: 60px;
  }

  .value {
    color: #ffffff;
  }
`;

const ReservationInfo = styled.div`
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 0.8rem;
  margin-bottom: 1rem;
  border-left: 3px solid ${props => getStatusColor(props.status)};

  .customer {
    font-weight: 600;
    color: #00ffff;
    margin-bottom: 0.3rem;
  }

  .time {
    font-size: 0.8rem;
    color: #b0b0b0;
  }
`;

const ActionButton = styled.button`
  width: 100%;
  padding: 1rem;
  background: ${props => props.disabled 
    ? 'rgba(100, 100, 100, 0.3)' 
    : 'linear-gradient(45deg, #00ffff, #8a2be2)'};
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 255, 0.4);
  }

  &:disabled {
    opacity: 0.6;
  }
`;

const ConnectionStatus = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => props.connected ? '#00ff00' : '#ff0040'};
  box-shadow: 0 0 10px currentColor;
  animation: ${props => props.connected ? 'pulse 2s infinite' : 'none'};
`;

// Helper functions
const getStatusColor = (status) => {
  switch (status) {
    case 'available': return '#00ff00';
    case 'in_use': return '#ff6600';
    case 'reserved': return '#ffff00';
    case 'maintenance': return '#ff0040';
    default: return '#666666';
  }
};

const getStatusGradient = (status) => {
  switch (status) {
    case 'available': return 'linear-gradient(45deg, #00ff00, #00cc00)';
    case 'in_use': return 'linear-gradient(45deg, #ff6600, #ff4400)';
    case 'reserved': return 'linear-gradient(45deg, #ffff00, #ffcc00)';
    case 'maintenance': return 'linear-gradient(45deg, #ff0040, #cc0030)';
    default: return 'linear-gradient(45deg, #666666, #444444)';
  }
};

const getStatusText = (status) => {
  switch (status) {
    case 'available': return 'Available';
    case 'in_use': return 'In Use';
    case 'reserved': return 'Reserved';
    case 'maintenance': return 'Maintenance';
    default: return 'Unknown';
  }
};

const getActionText = (status) => {
  switch (status) {
    case 'available': return 'Reserve Now';
    case 'in_use': return 'Currently In Use';
    case 'reserved': return 'Already Reserved';
    case 'maintenance': return 'Under Maintenance';
    default: return 'Unavailable';
  }
};

const PCCard = ({ pc, index, connected }) => {
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);

  const handleReserve = () => {
    if (pc.current_status === 'available') {
      navigate(`/reserve/${pc.id}`);
    }
  };

  const formatTime = (timeString) => {
    if (!timeString) return '';
    const date = new Date(timeString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const specs = pc.specifications || {};

  return (
    <CardContainer
      status={pc.current_status}
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ scale: 1.02 }}
    >
      <ConnectionStatus connected={connected} />
      
      <PCHeader>
        <PCNumber>PC {pc.pc_number}</PCNumber>
        <StatusBadge status={pc.current_status}>
          {getStatusText(pc.current_status)}
        </StatusBadge>
      </PCHeader>

      <PCName>{pc.name}</PCName>

      <SpecsList>
        <SpecItem>
          <Cpu className="icon" />
          <span className="label">CPU:</span>
          <span className="value">{specs.cpu || 'N/A'}</span>
        </SpecItem>
        <SpecItem>
          <Monitor className="icon" />
          <span className="label">GPU:</span>
          <span className="value">{specs.gpu || 'N/A'}</span>
        </SpecItem>
        <SpecItem>
          <Zap className="icon" />
          <span className="label">RAM:</span>
          <span className="value">{specs.ram || 'N/A'}</span>
        </SpecItem>
        <SpecItem>
          <HardDrive className="icon" />
          <span className="label">Storage:</span>
          <span className="value">{specs.storage || 'N/A'}</span>
        </SpecItem>
      </SpecsList>

      {(pc.current_status === 'in_use' || pc.current_status === 'reserved') && pc.customer_name && (
        <ReservationInfo status={pc.current_status}>
          <div className="customer">{pc.customer_name}</div>
          <div className="time">
            {formatTime(pc.start_time)} - {formatTime(pc.end_time)}
          </div>
        </ReservationInfo>
      )}

      <ActionButton
        disabled={pc.current_status !== 'available'}
        onClick={handleReserve}
      >
        {getActionText(pc.current_status)}
      </ActionButton>
    </CardContainer>
  );
};

export default PCCard;
