[{"C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\pages\\ReservationForm.js": "3", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\pages\\Home.js": "4", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\contexts\\AuthContext.js": "5", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\pages\\AdminLogin.js": "6", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\pages\\AdminDashboard.js": "7", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\contexts\\SocketContext.js": "8", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\styles\\GlobalStyles.js": "9", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\components\\Layout\\Footer.js": "10", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\components\\Layout\\Header.js": "11", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\components\\Auth\\ProtectedRoute.js": "12", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\components\\UI\\LoadingSpinner.js": "13", "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\components\\PC\\PCCard.js": "14"}, {"size": 232, "mtime": 1754057498079, "results": "15", "hashOfConfig": "16"}, {"size": 4355, "mtime": 1754057255887, "results": "17", "hashOfConfig": "16"}, {"size": 11033, "mtime": 1754057627447, "results": "18", "hashOfConfig": "16"}, {"size": 7596, "mtime": 1754057434787, "results": "19", "hashOfConfig": "16"}, {"size": 4248, "mtime": 1754057306843, "results": "20", "hashOfConfig": "16"}, {"size": 5758, "mtime": 1754057550939, "results": "21", "hashOfConfig": "16"}, {"size": 5144, "mtime": 1754057578403, "results": "22", "hashOfConfig": "16"}, {"size": 4959, "mtime": 1754057330822, "results": "23", "hashOfConfig": "16"}, {"size": 5297, "mtime": 1754057286811, "results": "24", "hashOfConfig": "16"}, {"size": 2467, "mtime": 1754057515066, "results": "25", "hashOfConfig": "16"}, {"size": 5668, "mtime": 1754057361868, "results": "26", "hashOfConfig": "16"}, {"size": 595, "mtime": 1754057523709, "results": "27", "hashOfConfig": "16"}, {"size": 2265, "mtime": 1754057492039, "results": "28", "hashOfConfig": "16"}, {"size": 7404, "mtime": 1754057474935, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ai3a9c", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\pages\\ReservationForm.js", ["72", "73"], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\pages\\AdminLogin.js", [], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\pages\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\contexts\\SocketContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\styles\\GlobalStyles.js", [], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\components\\Layout\\Footer.js", ["74", "75", "76", "77", "78"], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\components\\Layout\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\components\\Auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\components\\UI\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\hamza\\frontend\\src\\components\\PC\\PCCard.js", ["79"], [], {"ruleId": "80", "severity": 1, "message": "81", "line": 226, "column": 6, "nodeType": "82", "endLine": 226, "endColumn": 12, "suggestions": "83"}, {"ruleId": "80", "severity": 1, "message": "84", "line": 230, "column": 6, "nodeType": "82", "endLine": 230, "endColumn": 31, "suggestions": "85"}, {"ruleId": "86", "severity": 1, "message": "87", "line": 3, "column": 10, "nodeType": "88", "messageId": "89", "endLine": 3, "endColumn": 16}, {"ruleId": "90", "severity": 1, "message": "91", "line": 97, "column": 17, "nodeType": "92", "endLine": 97, "endColumn": 29}, {"ruleId": "90", "severity": 1, "message": "91", "line": 98, "column": 17, "nodeType": "92", "endLine": 98, "endColumn": 29}, {"ruleId": "90", "severity": 1, "message": "91", "line": 99, "column": 17, "nodeType": "92", "endLine": 99, "endColumn": 29}, {"ruleId": "90", "severity": 1, "message": "91", "line": 100, "column": 17, "nodeType": "92", "endLine": 100, "endColumn": 29}, {"ruleId": "86", "severity": 1, "message": "93", "line": 213, "column": 10, "nodeType": "88", "messageId": "89", "endLine": 213, "endColumn": 19}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchPC'. Either include it or remove the dependency array.", "ArrayExpression", ["94"], "React Hook useEffect has a missing dependency: 'calculateCost'. Either include it or remove the dependency array.", ["95"], "no-unused-vars", "'motion' is defined but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'isHovered' is assigned a value but never used.", {"desc": "96", "fix": "97"}, {"desc": "98", "fix": "99"}, "Update the dependencies array to be: [fetchPC, pcId]", {"range": "100", "text": "101"}, "Update the dependencies array to be: [calculateCost, formData.duration_hours]", {"range": "102", "text": "103"}, [4602, 4608], "[fetchPC, pcId]", [4658, 4683], "[calculateCost, formData.duration_hours]"]