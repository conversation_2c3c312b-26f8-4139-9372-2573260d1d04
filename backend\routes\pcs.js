const express = require('express');
const { body, validationResult } = require('express-validator');
const { executeQuery } = require('../config/database');
const { verifyToken, verifyAdmin } = require('../middleware/auth');

const router = express.Router();

// Get all PCs with their current status
router.get('/', async (req, res) => {
    try {
        const query = `
            SELECT 
                p.*,
                CASE 
                    WHEN r.id IS NOT NULL AND r.status = 'confirmed' AND NOW() BETWEEN r.start_time AND r.end_time 
                    THEN 'in_use'
                    WHEN r.id IS NOT NULL AND r.status = 'confirmed' AND r.start_time > NOW()
                    THEN 'reserved'
                    ELSE p.status
                END as current_status,
                r.customer_name,
                r.start_time,
                r.end_time
            FROM pcs p
            LEFT JOIN reservations r ON p.id = r.pc_id 
                AND r.status = 'confirmed' 
                AND (
                    (NOW() BETWEEN r.start_time AND r.end_time) OR
                    (r.start_time > NOW() AND r.start_time <= DATE_ADD(NOW(), INTERVAL 1 DAY))
                )
            ORDER BY p.pc_number
        `;
        
        const pcs = await executeQuery(query);
        
        // Parse JSON specifications
        const formattedPcs = pcs.map(pc => ({
            ...pc,
            specifications: typeof pc.specifications === 'string' 
                ? JSON.parse(pc.specifications) 
                : pc.specifications
        }));

        res.json(formattedPcs);
    } catch (error) {
        console.error('Error fetching PCs:', error);
        res.status(500).json({ error: 'Failed to fetch PCs' });
    }
});

// Get specific PC by ID
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const query = `
            SELECT 
                p.*,
                CASE 
                    WHEN r.id IS NOT NULL AND r.status = 'confirmed' AND NOW() BETWEEN r.start_time AND r.end_time 
                    THEN 'in_use'
                    WHEN r.id IS NOT NULL AND r.status = 'confirmed' AND r.start_time > NOW()
                    THEN 'reserved'
                    ELSE p.status
                END as current_status,
                r.customer_name,
                r.start_time,
                r.end_time
            FROM pcs p
            LEFT JOIN reservations r ON p.id = r.pc_id 
                AND r.status = 'confirmed' 
                AND (
                    (NOW() BETWEEN r.start_time AND r.end_time) OR
                    (r.start_time > NOW())
                )
            WHERE p.id = ?
        `;
        
        const [pc] = await executeQuery(query, [id]);
        
        if (!pc) {
            return res.status(404).json({ error: 'PC not found' });
        }

        // Parse JSON specifications
        pc.specifications = typeof pc.specifications === 'string' 
            ? JSON.parse(pc.specifications) 
            : pc.specifications;

        res.json(pc);
    } catch (error) {
        console.error('Error fetching PC:', error);
        res.status(500).json({ error: 'Failed to fetch PC' });
    }
});

// Update PC status (Admin only)
router.put('/:id/status', 
    verifyToken, 
    verifyAdmin,
    [
        body('status').isIn(['available', 'in_use', 'reserved', 'maintenance'])
            .withMessage('Invalid status')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ errors: errors.array() });
            }

            const { id } = req.params;
            const { status } = req.body;

            const updateQuery = 'UPDATE pcs SET status = ?, updated_at = NOW() WHERE id = ?';
            const result = await executeQuery(updateQuery, [status, id]);

            if (result.affectedRows === 0) {
                return res.status(404).json({ error: 'PC not found' });
            }

            // Get updated PC data
            const [updatedPc] = await executeQuery('SELECT * FROM pcs WHERE id = ?', [id]);
            
            // Broadcast status update to all connected clients
            req.io.emit('pc-status-update', {
                pcId: parseInt(id),
                status: status,
                timestamp: new Date().toISOString()
            });

            res.json({ 
                message: 'PC status updated successfully', 
                pc: updatedPc 
            });
        } catch (error) {
            console.error('Error updating PC status:', error);
            res.status(500).json({ error: 'Failed to update PC status' });
        }
    }
);

// Get PC availability for a specific time range
router.get('/:id/availability', async (req, res) => {
    try {
        const { id } = req.params;
        const { date } = req.query;

        if (!date) {
            return res.status(400).json({ error: 'Date parameter is required' });
        }

        const query = `
            SELECT start_time, end_time 
            FROM reservations 
            WHERE pc_id = ? 
                AND DATE(start_time) = DATE(?) 
                AND status IN ('confirmed', 'pending')
            ORDER BY start_time
        `;

        const reservations = await executeQuery(query, [id, date]);
        
        res.json({
            date,
            reservations: reservations.map(r => ({
                start: r.start_time,
                end: r.end_time
            }))
        });
    } catch (error) {
        console.error('Error fetching PC availability:', error);
        res.status(500).json({ error: 'Failed to fetch PC availability' });
    }
});

module.exports = router;
