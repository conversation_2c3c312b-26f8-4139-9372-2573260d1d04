const express = require('express');
const { body, validationResult } = require('express-validator');
const { executeQuery } = require('../config/database');
const { verifyToken, verifyAdmin, verifySuperAdmin } = require('../middleware/auth');

const router = express.Router();

// All admin routes require authentication
router.use(verifyToken);
router.use(verifyAdmin);

// Get dashboard statistics
router.get('/dashboard', async (req, res) => {
    try {
        // Get various statistics for the dashboard
        const [totalPCs] = await executeQuery('SELECT COUNT(*) as count FROM pcs');
        const [availablePCs] = await executeQuery('SELECT COUNT(*) as count FROM pcs WHERE status = "available"');
        const [maintenancePCs] = await executeQuery('SELECT COUNT(*) as count FROM pcs WHERE status = "maintenance"');
        
        const [todayReservations] = await executeQuery(`
            SELECT COUNT(*) as count FROM reservations 
            WHERE DATE(start_time) = CURDATE() AND status IN ('confirmed', 'pending')
        `);
        
        const [activeReservations] = await executeQuery(`
            SELECT COUNT(*) as count FROM reservations 
            WHERE NOW() BETWEEN start_time AND end_time AND status = 'confirmed'
        `);
        
        const [todayRevenue] = await executeQuery(`
            SELECT COALESCE(SUM(total_cost), 0) as revenue FROM reservations 
            WHERE DATE(start_time) = CURDATE() AND status = 'confirmed'
        `);

        // Get recent reservations
        const recentReservations = await executeQuery(`
            SELECT 
                r.*,
                p.pc_number,
                p.name as pc_name
            FROM reservations r
            JOIN pcs p ON r.pc_id = p.id
            WHERE r.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ORDER BY r.created_at DESC
            LIMIT 10
        `);

        res.json({
            statistics: {
                totalPCs: totalPCs.count,
                availablePCs: availablePCs.count,
                maintenancePCs: maintenancePCs.count,
                todayReservations: todayReservations.count,
                activeReservations: activeReservations.count,
                todayRevenue: parseFloat(todayRevenue.revenue)
            },
            recentReservations
        });
    } catch (error) {
        console.error('Error fetching dashboard data:', error);
        res.status(500).json({ error: 'Failed to fetch dashboard data' });
    }
});

// Get all reservations with advanced filtering
router.get('/reservations', async (req, res) => {
    try {
        const { 
            status, 
            date_from, 
            date_to, 
            pc_id, 
            customer_name,
            page = 1, 
            limit = 50 
        } = req.query;

        let query = `
            SELECT 
                r.*,
                p.pc_number,
                p.name as pc_name
            FROM reservations r
            JOIN pcs p ON r.pc_id = p.id
            WHERE 1=1
        `;
        const params = [];

        if (status) {
            query += ' AND r.status = ?';
            params.push(status);
        }

        if (date_from) {
            query += ' AND DATE(r.start_time) >= DATE(?)';
            params.push(date_from);
        }

        if (date_to) {
            query += ' AND DATE(r.start_time) <= DATE(?)';
            params.push(date_to);
        }

        if (pc_id) {
            query += ' AND r.pc_id = ?';
            params.push(pc_id);
        }

        if (customer_name) {
            query += ' AND r.customer_name LIKE ?';
            params.push(`%${customer_name}%`);
        }

        query += ' ORDER BY r.start_time DESC';

        // Add pagination
        const offset = (page - 1) * limit;
        query += ' LIMIT ? OFFSET ?';
        params.push(parseInt(limit), offset);

        const reservations = await executeQuery(query, params);

        // Get total count for pagination
        let countQuery = query.replace(/SELECT.*?FROM/, 'SELECT COUNT(*) as total FROM');
        countQuery = countQuery.replace(/ORDER BY.*?LIMIT.*?OFFSET.*?$/, '');
        const countParams = params.slice(0, -2); // Remove limit and offset params
        
        const [{ total }] = await executeQuery(countQuery, countParams);

        res.json({
            reservations,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching admin reservations:', error);
        res.status(500).json({ error: 'Failed to fetch reservations' });
    }
});

// Update reservation status
router.put('/reservations/:id/status',
    [
        body('status').isIn(['pending', 'confirmed', 'in_progress', 'completed', 'cancelled'])
            .withMessage('Invalid status')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ errors: errors.array() });
            }

            const { id } = req.params;
            const { status } = req.body;

            const result = await executeQuery(
                'UPDATE reservations SET status = ?, updated_at = NOW() WHERE id = ?',
                [status, id]
            );

            if (result.affectedRows === 0) {
                return res.status(404).json({ error: 'Reservation not found' });
            }

            // Get updated reservation
            const [updatedReservation] = await executeQuery(`
                SELECT 
                    r.*,
                    p.pc_number,
                    p.name as pc_name
                FROM reservations r
                JOIN pcs p ON r.pc_id = p.id
                WHERE r.id = ?
            `, [id]);

            // Broadcast status update
            req.io.emit('reservation-status-update', {
                reservationId: parseInt(id),
                status,
                reservation: updatedReservation,
                timestamp: new Date().toISOString()
            });

            res.json({
                message: 'Reservation status updated successfully',
                reservation: updatedReservation
            });
        } catch (error) {
            console.error('Error updating reservation status:', error);
            res.status(500).json({ error: 'Failed to update reservation status' });
        }
    }
);

// Get system settings
router.get('/settings', async (req, res) => {
    try {
        const settings = await executeQuery('SELECT * FROM settings ORDER BY setting_key');
        
        // Convert to key-value object
        const settingsObj = {};
        settings.forEach(setting => {
            try {
                // Try to parse JSON values
                settingsObj[setting.setting_key] = JSON.parse(setting.setting_value);
            } catch {
                // If not JSON, keep as string
                settingsObj[setting.setting_key] = setting.setting_value;
            }
        });

        res.json(settingsObj);
    } catch (error) {
        console.error('Error fetching settings:', error);
        res.status(500).json({ error: 'Failed to fetch settings' });
    }
});

// Update system settings (Super Admin only)
router.put('/settings', 
    verifySuperAdmin,
    [
        body('settings').isObject().withMessage('Settings must be an object')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ errors: errors.array() });
            }

            const { settings } = req.body;

            // Update each setting
            for (const [key, value] of Object.entries(settings)) {
                const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
                
                await executeQuery(`
                    INSERT INTO settings (setting_key, setting_value, updated_at)
                    VALUES (?, ?, NOW())
                    ON DUPLICATE KEY UPDATE 
                    setting_value = VALUES(setting_value),
                    updated_at = NOW()
                `, [key, stringValue]);
            }

            res.json({ message: 'Settings updated successfully' });
        } catch (error) {
            console.error('Error updating settings:', error);
            res.status(500).json({ error: 'Failed to update settings' });
        }
    }
);

// Get revenue reports
router.get('/reports/revenue', async (req, res) => {
    try {
        const { period = 'week' } = req.query;
        
        let dateCondition;
        switch (period) {
            case 'today':
                dateCondition = 'DATE(start_time) = CURDATE()';
                break;
            case 'week':
                dateCondition = 'start_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                break;
            case 'month':
                dateCondition = 'start_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                break;
            default:
                dateCondition = 'start_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        }

        const revenueData = await executeQuery(`
            SELECT 
                DATE(start_time) as date,
                COUNT(*) as reservations,
                SUM(total_cost) as revenue,
                AVG(duration_hours) as avg_duration
            FROM reservations 
            WHERE ${dateCondition} AND status = 'confirmed'
            GROUP BY DATE(start_time)
            ORDER BY date DESC
        `);

        const [totalRevenue] = await executeQuery(`
            SELECT 
                COUNT(*) as total_reservations,
                SUM(total_cost) as total_revenue
            FROM reservations 
            WHERE ${dateCondition} AND status = 'confirmed'
        `);

        res.json({
            period,
            summary: totalRevenue,
            daily: revenueData
        });
    } catch (error) {
        console.error('Error fetching revenue reports:', error);
        res.status(500).json({ error: 'Failed to fetch revenue reports' });
    }
});

module.exports = router;
