{"ast": null, "code": "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" || typeof Blob !== \"undefined\" && Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n  return typeof ArrayBuffer.isView === \"function\" ? ArrayBuffer.isView(obj) : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({\n  type,\n  data\n}, supportsBinary, callback) => {\n  if (withNativeBlob && data instanceof Blob) {\n    if (supportsBinary) {\n      return callback(data);\n    } else {\n      return encodeBlobAsBase64(data, callback);\n    }\n  } else if (withNativeArrayBuffer && (data instanceof ArrayBuffer || isView(data))) {\n    if (supportsBinary) {\n      return callback(data);\n    } else {\n      return encodeBlobAsBase64(new Blob([data]), callback);\n    }\n  }\n  // plain string\n  return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n  const fileReader = new FileReader();\n  fileReader.onload = function () {\n    const content = fileReader.result.split(\",\")[1];\n    callback(\"b\" + (content || \"\"));\n  };\n  return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n  if (data instanceof Uint8Array) {\n    return data;\n  } else if (data instanceof ArrayBuffer) {\n    return new Uint8Array(data);\n  } else {\n    return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n  }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n  if (withNativeBlob && packet.data instanceof Blob) {\n    return packet.data.arrayBuffer().then(toArray).then(callback);\n  } else if (withNativeArrayBuffer && (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n    return callback(toArray(packet.data));\n  }\n  encodePacket(packet, false, encoded => {\n    if (!TEXT_ENCODER) {\n      TEXT_ENCODER = new TextEncoder();\n    }\n    callback(TEXT_ENCODER.encode(encoded));\n  });\n}\nexport { encodePacket };", "map": {"version": 3, "names": ["PACKET_TYPES", "withNativeBlob", "Blob", "Object", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "type", "data", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "TEXT_ENCODER", "encodePacketToBinary", "packet", "arrayBuffer", "then", "encoded", "TextEncoder", "encode"], "sources": ["C:/Users/<USER>/Desktop/hamza/frontend/node_modules/engine.io-parser/build/esm/encodePacket.browser.js"], "sourcesContent": ["import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,cAAc;AAC3C,MAAMC,cAAc,GAAG,OAAOC,IAAI,KAAK,UAAU,IAC5C,OAAOA,IAAI,KAAK,WAAW,IACxBC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,IAAI,CAAC,KAAK,0BAA2B;AAC5E,MAAMK,qBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU;AAC/D;AACA,MAAMC,MAAM,GAAIC,GAAG,IAAK;EACpB,OAAO,OAAOF,WAAW,CAACC,MAAM,KAAK,UAAU,GACzCD,WAAW,CAACC,MAAM,CAACC,GAAG,CAAC,GACvBA,GAAG,IAAIA,GAAG,CAACC,MAAM,YAAYH,WAAW;AAClD,CAAC;AACD,MAAMI,YAAY,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAK,CAAC,EAAEC,cAAc,EAAEC,QAAQ,KAAK;EAC/D,IAAIf,cAAc,IAAIa,IAAI,YAAYZ,IAAI,EAAE;IACxC,IAAIa,cAAc,EAAE;MAChB,OAAOC,QAAQ,CAACF,IAAI,CAAC;IACzB,CAAC,MACI;MACD,OAAOG,kBAAkB,CAACH,IAAI,EAAEE,QAAQ,CAAC;IAC7C;EACJ,CAAC,MACI,IAAIT,qBAAqB,KACzBO,IAAI,YAAYN,WAAW,IAAIC,MAAM,CAACK,IAAI,CAAC,CAAC,EAAE;IAC/C,IAAIC,cAAc,EAAE;MAChB,OAAOC,QAAQ,CAACF,IAAI,CAAC;IACzB,CAAC,MACI;MACD,OAAOG,kBAAkB,CAAC,IAAIf,IAAI,CAAC,CAACY,IAAI,CAAC,CAAC,EAAEE,QAAQ,CAAC;IACzD;EACJ;EACA;EACA,OAAOA,QAAQ,CAAChB,YAAY,CAACa,IAAI,CAAC,IAAIC,IAAI,IAAI,EAAE,CAAC,CAAC;AACtD,CAAC;AACD,MAAMG,kBAAkB,GAAGA,CAACH,IAAI,EAAEE,QAAQ,KAAK;EAC3C,MAAME,UAAU,GAAG,IAAIC,UAAU,CAAC,CAAC;EACnCD,UAAU,CAACE,MAAM,GAAG,YAAY;IAC5B,MAAMC,OAAO,GAAGH,UAAU,CAACI,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/CP,QAAQ,CAAC,GAAG,IAAIK,OAAO,IAAI,EAAE,CAAC,CAAC;EACnC,CAAC;EACD,OAAOH,UAAU,CAACM,aAAa,CAACV,IAAI,CAAC;AACzC,CAAC;AACD,SAASW,OAAOA,CAACX,IAAI,EAAE;EACnB,IAAIA,IAAI,YAAYY,UAAU,EAAE;IAC5B,OAAOZ,IAAI;EACf,CAAC,MACI,IAAIA,IAAI,YAAYN,WAAW,EAAE;IAClC,OAAO,IAAIkB,UAAU,CAACZ,IAAI,CAAC;EAC/B,CAAC,MACI;IACD,OAAO,IAAIY,UAAU,CAACZ,IAAI,CAACH,MAAM,EAAEG,IAAI,CAACa,UAAU,EAAEb,IAAI,CAACc,UAAU,CAAC;EACxE;AACJ;AACA,IAAIC,YAAY;AAChB,OAAO,SAASC,oBAAoBA,CAACC,MAAM,EAAEf,QAAQ,EAAE;EACnD,IAAIf,cAAc,IAAI8B,MAAM,CAACjB,IAAI,YAAYZ,IAAI,EAAE;IAC/C,OAAO6B,MAAM,CAACjB,IAAI,CAACkB,WAAW,CAAC,CAAC,CAACC,IAAI,CAACR,OAAO,CAAC,CAACQ,IAAI,CAACjB,QAAQ,CAAC;EACjE,CAAC,MACI,IAAIT,qBAAqB,KACzBwB,MAAM,CAACjB,IAAI,YAAYN,WAAW,IAAIC,MAAM,CAACsB,MAAM,CAACjB,IAAI,CAAC,CAAC,EAAE;IAC7D,OAAOE,QAAQ,CAACS,OAAO,CAACM,MAAM,CAACjB,IAAI,CAAC,CAAC;EACzC;EACAF,YAAY,CAACmB,MAAM,EAAE,KAAK,EAAGG,OAAO,IAAK;IACrC,IAAI,CAACL,YAAY,EAAE;MACfA,YAAY,GAAG,IAAIM,WAAW,CAAC,CAAC;IACpC;IACAnB,QAAQ,CAACa,YAAY,CAACO,MAAM,CAACF,OAAO,CAAC,CAAC;EAC1C,CAAC,CAAC;AACN;AACA,SAAStB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}