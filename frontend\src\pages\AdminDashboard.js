import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import axios from 'axios';
import toast from 'react-hot-toast';
import LoadingSpinner from '../components/UI/LoadingSpinner';

const DashboardContainer = styled.div`
  min-height: 100vh;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const DashboardTitle = styled(motion.h1)`
  text-align: center;
  margin-bottom: 3rem;
  color: #00ffff;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
`;

const StatsGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
`;

const StatCard = styled.div`
  background: rgba(26, 26, 46, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;

  .number {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    font-weight: 700;
    color: #00ffff;
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
    margin-bottom: 0.5rem;
  }

  .label {
    font-size: 1.1rem;
    color: #b0b0b0;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
`;

const RecentReservations = styled(motion.div)`
  background: rgba(26, 26, 46, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 16px;
  padding: 2rem;
`;

const SectionTitle = styled.h2`
  color: #00ffff;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
`;

const ReservationList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const ReservationItem = styled.div`
  background: rgba(10, 10, 10, 0.3);
  border-radius: 8px;
  padding: 1rem;
  border-left: 3px solid #00ffff;

  .customer {
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.3rem;
  }

  .details {
    font-size: 0.9rem;
    color: #b0b0b0;
  }

  .pc {
    color: #00ffff;
    font-weight: 600;
  }
`;

const AdminDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/admin/dashboard');
      setDashboardData(response.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <DashboardContainer>
        <LoadingSpinner text="LOADING DASHBOARD" />
      </DashboardContainer>
    );
  }

  if (!dashboardData) {
    return (
      <DashboardContainer>
        <DashboardTitle>Error loading dashboard</DashboardTitle>
      </DashboardContainer>
    );
  }

  const { statistics, recentReservations } = dashboardData;

  return (
    <DashboardContainer>
      <DashboardTitle
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        ADMIN DASHBOARD
      </DashboardTitle>

      <StatsGrid
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
      >
        <StatCard>
          <div className="number">{statistics.totalPCs}</div>
          <div className="label">Total PCs</div>
        </StatCard>
        <StatCard>
          <div className="number">{statistics.availablePCs}</div>
          <div className="label">Available</div>
        </StatCard>
        <StatCard>
          <div className="number">{statistics.activeReservations}</div>
          <div className="label">Active Sessions</div>
        </StatCard>
        <StatCard>
          <div className="number">${statistics.todayRevenue.toFixed(2)}</div>
          <div className="label">Today's Revenue</div>
        </StatCard>
      </StatsGrid>

      <RecentReservations
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
      >
        <SectionTitle>Recent Reservations</SectionTitle>
        <ReservationList>
          {recentReservations.length > 0 ? (
            recentReservations.map((reservation) => (
              <ReservationItem key={reservation.id}>
                <div className="customer">{reservation.customer_name}</div>
                <div className="details">
                  <span className="pc">PC {reservation.pc_number}</span> • 
                  {new Date(reservation.start_time).toLocaleString()} • 
                  {reservation.duration_hours}h • 
                  ${reservation.total_cost}
                </div>
              </ReservationItem>
            ))
          ) : (
            <div style={{ color: '#666666', textAlign: 'center', padding: '2rem' }}>
              No recent reservations
            </div>
          )}
        </ReservationList>
      </RecentReservations>
    </DashboardContainer>
  );
};

export default AdminDashboard;
