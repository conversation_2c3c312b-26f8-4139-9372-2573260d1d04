import { createGlobalStyle } from 'styled-components';

const GlobalStyles = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Raj<PERSON>ni', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Cyberpunk Color Variables */
  :root {
    --primary-cyan: #00ffff;
    --primary-purple: #8a2be2;
    --primary-green: #00ff00;
    --accent-pink: #ff0080;
    --accent-orange: #ff6600;
    --dark-bg: #0a0a0a;
    --dark-secondary: #1a1a2e;
    --dark-tertiary: #16213e;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #666666;
    --border-glow: rgba(0, 255, 255, 0.3);
    --shadow-glow: 0 0 20px rgba(0, 255, 255, 0.2);
  }

  /* Scrollbar Styling */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--dark-secondary);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, var(--primary-cyan), var(--primary-purple));
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, var(--primary-purple), var(--primary-cyan));
  }

  /* Selection Styling */
  ::selection {
    background: rgba(0, 255, 255, 0.3);
    color: #ffffff;
  }

  ::-moz-selection {
    background: rgba(0, 255, 255, 0.3);
    color: #ffffff;
  }

  /* Typography */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
  }

  h1 {
    font-size: 3rem;
    background: linear-gradient(45deg, var(--primary-cyan), var(--primary-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
  }

  h2 {
    font-size: 2.5rem;
    color: var(--primary-cyan);
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
  }

  h3 {
    font-size: 2rem;
    color: var(--text-primary);
  }

  p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
  }

  /* Links */
  a {
    color: var(--primary-cyan);
    text-decoration: none;
    transition: all 0.3s ease;
  }

  a:hover {
    color: var(--primary-purple);
    text-shadow: 0 0 10px currentColor;
  }

  /* Buttons */
  button {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    cursor: pointer;
    border: none;
    outline: none;
    transition: all 0.3s ease;
  }

  /* Form Elements */
  input, textarea, select {
    font-family: 'Rajdhani', sans-serif;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
  }

  input:focus, textarea:focus, select:focus {
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.4);
  }

  /* Utility Classes */
  .text-glow {
    text-shadow: 0 0 10px currentColor;
  }

  .border-glow {
    box-shadow: 0 0 15px var(--border-glow);
  }

  .bg-glass {
    background: rgba(26, 26, 46, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 255, 255, 0.2);
  }

  .bg-glass-dark {
    background: rgba(10, 10, 10, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 255, 255, 0.1);
  }

  /* Animations */
  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
    }
    50% {
      box-shadow: 0 0 25px rgba(0, 255, 255, 0.6);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes slideInFromLeft {
    0% {
      transform: translateX(-100%);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideInFromRight {
    0% {
      transform: translateX(100%);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes fadeInUp {
    0% {
      transform: translateY(30px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes neonFlicker {
    0%, 100% {
      text-shadow: 
        0 0 5px currentColor,
        0 0 10px currentColor,
        0 0 15px currentColor;
    }
    50% {
      text-shadow: 
        0 0 2px currentColor,
        0 0 5px currentColor,
        0 0 8px currentColor;
    }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    h1 {
      font-size: 2.5rem;
    }
    
    h2 {
      font-size: 2rem;
    }
    
    h3 {
      font-size: 1.5rem;
    }
  }

  @media (max-width: 480px) {
    h1 {
      font-size: 2rem;
    }
    
    h2 {
      font-size: 1.5rem;
    }
  }

  /* Loading States */
  .loading {
    position: relative;
    overflow: hidden;
  }

  .loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 255, 255, 0.2),
      transparent
    );
    animation: loading-shimmer 1.5s infinite;
  }

  @keyframes loading-shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }
`;

export default GlobalStyles;
