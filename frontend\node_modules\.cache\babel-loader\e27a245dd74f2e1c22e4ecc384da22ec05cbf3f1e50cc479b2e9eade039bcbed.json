{"ast": null, "code": "function isIdentityScale(scale) {\n  return scale === undefined || scale === 1;\n}\nfunction hasScale({\n  scale,\n  scaleX,\n  scaleY\n}) {\n  return !isIdentityScale(scale) || !isIdentityScale(scaleX) || !isIdentityScale(scaleY);\n}\nfunction hasTransform(values) {\n  return hasScale(values) || has2DTranslate(values) || values.z || values.rotate || values.rotateX || values.rotateY;\n}\nfunction has2DTranslate(values) {\n  return is2DTranslate(values.x) || is2DTranslate(values.y);\n}\nfunction is2DTranslate(value) {\n  return value && value !== \"0%\";\n}\nexport { has2DTranslate, hasScale, hasTransform };", "map": {"version": 3, "names": ["isIdentityScale", "scale", "undefined", "hasScale", "scaleX", "scaleY", "hasTransform", "values", "has2DTranslate", "z", "rotate", "rotateX", "rotateY", "is2DTranslate", "x", "y", "value"], "sources": ["C:/Users/<USER>/Desktop/hamza/frontend/node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs"], "sourcesContent": ["function isIdentityScale(scale) {\n    return scale === undefined || scale === 1;\n}\nfunction hasScale({ scale, scaleX, scaleY }) {\n    return (!isIdentityScale(scale) ||\n        !isIdentityScale(scaleX) ||\n        !isIdentityScale(scaleY));\n}\nfunction hasTransform(values) {\n    return (hasScale(values) ||\n        has2DTranslate(values) ||\n        values.z ||\n        values.rotate ||\n        values.rotateX ||\n        values.rotateY);\n}\nfunction has2DTranslate(values) {\n    return is2DTranslate(values.x) || is2DTranslate(values.y);\n}\nfunction is2DTranslate(value) {\n    return value && value !== \"0%\";\n}\n\nexport { has2DTranslate, hasScale, hasTransform };\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,CAAC;AAC7C;AACA,SAASE,QAAQA,CAAC;EAAEF,KAAK;EAAEG,MAAM;EAAEC;AAAO,CAAC,EAAE;EACzC,OAAQ,CAACL,eAAe,CAACC,KAAK,CAAC,IAC3B,CAACD,eAAe,CAACI,MAAM,CAAC,IACxB,CAACJ,eAAe,CAACK,MAAM,CAAC;AAChC;AACA,SAASC,YAAYA,CAACC,MAAM,EAAE;EAC1B,OAAQJ,QAAQ,CAACI,MAAM,CAAC,IACpBC,cAAc,CAACD,MAAM,CAAC,IACtBA,MAAM,CAACE,CAAC,IACRF,MAAM,CAACG,MAAM,IACbH,MAAM,CAACI,OAAO,IACdJ,MAAM,CAACK,OAAO;AACtB;AACA,SAASJ,cAAcA,CAACD,MAAM,EAAE;EAC5B,OAAOM,aAAa,CAACN,MAAM,CAACO,CAAC,CAAC,IAAID,aAAa,CAACN,MAAM,CAACQ,CAAC,CAAC;AAC7D;AACA,SAASF,aAAaA,CAACG,KAAK,EAAE;EAC1B,OAAOA,KAAK,IAAIA,KAAK,KAAK,IAAI;AAClC;AAEA,SAASR,cAAc,EAAEL,QAAQ,EAAEG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}