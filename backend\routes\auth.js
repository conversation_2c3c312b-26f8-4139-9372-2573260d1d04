const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const { executeQuery } = require('../config/database');
const { verifyToken } = require('../middleware/auth');

const router = express.Router();

// Admin login
router.post('/admin/login',
    [
        body('email').isEmail().withMessage('Valid email is required'),
        body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ errors: errors.array() });
            }

            const { email, password } = req.body;

            // Find admin by email
            const [admin] = await executeQuery(
                'SELECT * FROM admins WHERE email = ? AND is_active = TRUE',
                [email]
            );

            if (!admin) {
                return res.status(401).json({ error: 'Invalid credentials' });
            }

            // Check password
            const isValidPassword = await bcrypt.compare(password, admin.password_hash);
            if (!isValidPassword) {
                return res.status(401).json({ error: 'Invalid credentials' });
            }

            // Generate JWT token
            const token = jwt.sign(
                { 
                    id: admin.id, 
                    email: admin.email, 
                    role: admin.role,
                    type: 'admin'
                },
                process.env.JWT_SECRET,
                { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
            );

            // Remove password from response
            const { password_hash, ...adminData } = admin;

            res.json({
                message: 'Login successful',
                token,
                admin: adminData
            });

        } catch (error) {
            console.error('Admin login error:', error);
            res.status(500).json({ error: 'Login failed' });
        }
    }
);

// User registration (optional feature)
router.post('/register',
    [
        body('username').trim().isLength({ min: 3, max: 50 })
            .withMessage('Username must be 3-50 characters'),
        body('email').isEmail().withMessage('Valid email is required'),
        body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
        body('phone').optional().isMobilePhone().withMessage('Valid phone number is required')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ errors: errors.array() });
            }

            const { username, email, password, phone } = req.body;

            // Check if user already exists
            const [existingUser] = await executeQuery(
                'SELECT id FROM users WHERE email = ? OR username = ?',
                [email, username]
            );

            if (existingUser) {
                return res.status(409).json({ error: 'User already exists with this email or username' });
            }

            // Hash password
            const saltRounds = 10;
            const passwordHash = await bcrypt.hash(password, saltRounds);

            // Create user
            const [result] = await executeQuery(`
                INSERT INTO users (username, email, password_hash, phone)
                VALUES (?, ?, ?, ?)
            `, [username, email, passwordHash, phone]);

            // Generate JWT token
            const token = jwt.sign(
                { 
                    id: result.insertId, 
                    email, 
                    username,
                    type: 'user'
                },
                process.env.JWT_SECRET,
                { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
            );

            res.status(201).json({
                message: 'User registered successfully',
                token,
                user: {
                    id: result.insertId,
                    username,
                    email,
                    phone
                }
            });

        } catch (error) {
            console.error('User registration error:', error);
            res.status(500).json({ error: 'Registration failed' });
        }
    }
);

// User login (optional feature)
router.post('/login',
    [
        body('email').isEmail().withMessage('Valid email is required'),
        body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ errors: errors.array() });
            }

            const { email, password } = req.body;

            // Find user by email
            const [user] = await executeQuery(
                'SELECT * FROM users WHERE email = ? AND is_active = TRUE',
                [email]
            );

            if (!user) {
                return res.status(401).json({ error: 'Invalid credentials' });
            }

            // Check password
            const isValidPassword = await bcrypt.compare(password, user.password_hash);
            if (!isValidPassword) {
                return res.status(401).json({ error: 'Invalid credentials' });
            }

            // Generate JWT token
            const token = jwt.sign(
                { 
                    id: user.id, 
                    email: user.email, 
                    username: user.username,
                    type: 'user'
                },
                process.env.JWT_SECRET,
                { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
            );

            // Remove password from response
            const { password_hash, ...userData } = user;

            res.json({
                message: 'Login successful',
                token,
                user: userData
            });

        } catch (error) {
            console.error('User login error:', error);
            res.status(500).json({ error: 'Login failed' });
        }
    }
);

// Verify token endpoint
router.get('/verify', verifyToken, async (req, res) => {
    try {
        if (req.user.type === 'admin') {
            const [admin] = await executeQuery(
                'SELECT id, username, email, role, is_active FROM admins WHERE id = ? AND is_active = TRUE',
                [req.user.id]
            );
            
            if (!admin) {
                return res.status(401).json({ error: 'Admin not found or inactive' });
            }
            
            res.json({ valid: true, user: admin, type: 'admin' });
        } else {
            const [user] = await executeQuery(
                'SELECT id, username, email, phone, is_active FROM users WHERE id = ? AND is_active = TRUE',
                [req.user.id]
            );
            
            if (!user) {
                return res.status(401).json({ error: 'User not found or inactive' });
            }
            
            res.json({ valid: true, user, type: 'user' });
        }
    } catch (error) {
        console.error('Token verification error:', error);
        res.status(500).json({ error: 'Token verification failed' });
    }
});

// Logout (client-side token removal, but we can track it server-side if needed)
router.post('/logout', verifyToken, (req, res) => {
    // In a more complex setup, you might want to blacklist the token
    res.json({ message: 'Logged out successfully' });
});

module.exports = router;
