{"ast": null, "code": "import { extractEventInfo } from '../../events/event-info.mjs';\nimport { secondsToMilliseconds, millisecondsToSeconds } from '../../utils/time-conversion.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { pipe } from '../../utils/pipe.mjs';\nimport { distance2D } from '../../utils/distance.mjs';\nimport { isPrimaryPointer } from '../../events/utils/is-primary-pointer.mjs';\nimport { frame, cancelFrame, frameData } from '../../frameloop/frame.mjs';\n\n/**\n * @internal\n */\nclass PanSession {\n  constructor(event, handlers, {\n    transformPagePoint,\n    contextWindow,\n    dragSnapToOrigin = false\n  } = {}) {\n    /**\n     * @internal\n     */\n    this.startEvent = null;\n    /**\n     * @internal\n     */\n    this.lastMoveEvent = null;\n    /**\n     * @internal\n     */\n    this.lastMoveEventInfo = null;\n    /**\n     * @internal\n     */\n    this.handlers = {};\n    /**\n     * @internal\n     */\n    this.contextWindow = window;\n    this.updatePoint = () => {\n      if (!(this.lastMoveEvent && this.lastMoveEventInfo)) return;\n      const info = getPanInfo(this.lastMoveEventInfo, this.history);\n      const isPanStarted = this.startEvent !== null;\n      // Only start panning if the offset is larger than 3 pixels. If we make it\n      // any larger than this we'll want to reset the pointer history\n      // on the first update to avoid visual snapping to the cursoe.\n      const isDistancePastThreshold = distance2D(info.offset, {\n        x: 0,\n        y: 0\n      }) >= 3;\n      if (!isPanStarted && !isDistancePastThreshold) return;\n      const {\n        point\n      } = info;\n      const {\n        timestamp\n      } = frameData;\n      this.history.push({\n        ...point,\n        timestamp\n      });\n      const {\n        onStart,\n        onMove\n      } = this.handlers;\n      if (!isPanStarted) {\n        onStart && onStart(this.lastMoveEvent, info);\n        this.startEvent = this.lastMoveEvent;\n      }\n      onMove && onMove(this.lastMoveEvent, info);\n    };\n    this.handlePointerMove = (event, info) => {\n      this.lastMoveEvent = event;\n      this.lastMoveEventInfo = transformPoint(info, this.transformPagePoint);\n      // Throttle mouse move event to once per frame\n      frame.update(this.updatePoint, true);\n    };\n    this.handlePointerUp = (event, info) => {\n      this.end();\n      const {\n        onEnd,\n        onSessionEnd,\n        resumeAnimation\n      } = this.handlers;\n      if (this.dragSnapToOrigin) resumeAnimation && resumeAnimation();\n      if (!(this.lastMoveEvent && this.lastMoveEventInfo)) return;\n      const panInfo = getPanInfo(event.type === \"pointercancel\" ? this.lastMoveEventInfo : transformPoint(info, this.transformPagePoint), this.history);\n      if (this.startEvent && onEnd) {\n        onEnd(event, panInfo);\n      }\n      onSessionEnd && onSessionEnd(event, panInfo);\n    };\n    // If we have more than one touch, don't start detecting this gesture\n    if (!isPrimaryPointer(event)) return;\n    this.dragSnapToOrigin = dragSnapToOrigin;\n    this.handlers = handlers;\n    this.transformPagePoint = transformPagePoint;\n    this.contextWindow = contextWindow || window;\n    const info = extractEventInfo(event);\n    const initialInfo = transformPoint(info, this.transformPagePoint);\n    const {\n      point\n    } = initialInfo;\n    const {\n      timestamp\n    } = frameData;\n    this.history = [{\n      ...point,\n      timestamp\n    }];\n    const {\n      onSessionStart\n    } = handlers;\n    onSessionStart && onSessionStart(event, getPanInfo(initialInfo, this.history));\n    this.removeListeners = pipe(addPointerEvent(this.contextWindow, \"pointermove\", this.handlePointerMove), addPointerEvent(this.contextWindow, \"pointerup\", this.handlePointerUp), addPointerEvent(this.contextWindow, \"pointercancel\", this.handlePointerUp));\n  }\n  updateHandlers(handlers) {\n    this.handlers = handlers;\n  }\n  end() {\n    this.removeListeners && this.removeListeners();\n    cancelFrame(this.updatePoint);\n  }\n}\nfunction transformPoint(info, transformPagePoint) {\n  return transformPagePoint ? {\n    point: transformPagePoint(info.point)\n  } : info;\n}\nfunction subtractPoint(a, b) {\n  return {\n    x: a.x - b.x,\n    y: a.y - b.y\n  };\n}\nfunction getPanInfo({\n  point\n}, history) {\n  return {\n    point,\n    delta: subtractPoint(point, lastDevicePoint(history)),\n    offset: subtractPoint(point, startDevicePoint(history)),\n    velocity: getVelocity(history, 0.1)\n  };\n}\nfunction startDevicePoint(history) {\n  return history[0];\n}\nfunction lastDevicePoint(history) {\n  return history[history.length - 1];\n}\nfunction getVelocity(history, timeDelta) {\n  if (history.length < 2) {\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  let i = history.length - 1;\n  let timestampedPoint = null;\n  const lastPoint = lastDevicePoint(history);\n  while (i >= 0) {\n    timestampedPoint = history[i];\n    if (lastPoint.timestamp - timestampedPoint.timestamp > secondsToMilliseconds(timeDelta)) {\n      break;\n    }\n    i--;\n  }\n  if (!timestampedPoint) {\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  const time = millisecondsToSeconds(lastPoint.timestamp - timestampedPoint.timestamp);\n  if (time === 0) {\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  const currentVelocity = {\n    x: (lastPoint.x - timestampedPoint.x) / time,\n    y: (lastPoint.y - timestampedPoint.y) / time\n  };\n  if (currentVelocity.x === Infinity) {\n    currentVelocity.x = 0;\n  }\n  if (currentVelocity.y === Infinity) {\n    currentVelocity.y = 0;\n  }\n  return currentVelocity;\n}\nexport { PanSession };", "map": {"version": 3, "names": ["extractEventInfo", "secondsToMilliseconds", "millisecondsToSeconds", "addPointerEvent", "pipe", "distance2D", "isPrimaryPointer", "frame", "cancelFrame", "frameData", "PanSession", "constructor", "event", "handlers", "transformPagePoint", "contextWindow", "dragSnapToO<PERSON>in", "startEvent", "lastMoveEvent", "lastMoveEventInfo", "window", "updatePoint", "info", "getPanInfo", "history", "isPanStarted", "isDistancePastThreshold", "offset", "x", "y", "point", "timestamp", "push", "onStart", "onMove", "handlePointerMove", "transformPoint", "update", "handlePointerUp", "end", "onEnd", "onSessionEnd", "resumeAnimation", "panInfo", "type", "initialInfo", "onSessionStart", "removeListeners", "updateHandlers", "subtractPoint", "a", "b", "delta", "lastDevicePoint", "startDevicePoint", "velocity", "getVelocity", "length", "<PERSON><PERSON><PERSON><PERSON>", "i", "timestampedPoint", "lastPoint", "time", "currentVelocity", "Infinity"], "sources": ["C:/Users/<USER>/Desktop/hamza/frontend/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs"], "sourcesContent": ["import { extractEventInfo } from '../../events/event-info.mjs';\nimport { secondsToMilliseconds, millisecondsToSeconds } from '../../utils/time-conversion.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { pipe } from '../../utils/pipe.mjs';\nimport { distance2D } from '../../utils/distance.mjs';\nimport { isPrimaryPointer } from '../../events/utils/is-primary-pointer.mjs';\nimport { frame, cancelFrame, frameData } from '../../frameloop/frame.mjs';\n\n/**\n * @internal\n */\nclass PanSession {\n    constructor(event, handlers, { transformPagePoint, contextWindow, dragSnapToOrigin = false } = {}) {\n        /**\n         * @internal\n         */\n        this.startEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEventInfo = null;\n        /**\n         * @internal\n         */\n        this.handlers = {};\n        /**\n         * @internal\n         */\n        this.contextWindow = window;\n        this.updatePoint = () => {\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const info = getPanInfo(this.lastMoveEventInfo, this.history);\n            const isPanStarted = this.startEvent !== null;\n            // Only start panning if the offset is larger than 3 pixels. If we make it\n            // any larger than this we'll want to reset the pointer history\n            // on the first update to avoid visual snapping to the cursoe.\n            const isDistancePastThreshold = distance2D(info.offset, { x: 0, y: 0 }) >= 3;\n            if (!isPanStarted && !isDistancePastThreshold)\n                return;\n            const { point } = info;\n            const { timestamp } = frameData;\n            this.history.push({ ...point, timestamp });\n            const { onStart, onMove } = this.handlers;\n            if (!isPanStarted) {\n                onStart && onStart(this.lastMoveEvent, info);\n                this.startEvent = this.lastMoveEvent;\n            }\n            onMove && onMove(this.lastMoveEvent, info);\n        };\n        this.handlePointerMove = (event, info) => {\n            this.lastMoveEvent = event;\n            this.lastMoveEventInfo = transformPoint(info, this.transformPagePoint);\n            // Throttle mouse move event to once per frame\n            frame.update(this.updatePoint, true);\n        };\n        this.handlePointerUp = (event, info) => {\n            this.end();\n            const { onEnd, onSessionEnd, resumeAnimation } = this.handlers;\n            if (this.dragSnapToOrigin)\n                resumeAnimation && resumeAnimation();\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const panInfo = getPanInfo(event.type === \"pointercancel\"\n                ? this.lastMoveEventInfo\n                : transformPoint(info, this.transformPagePoint), this.history);\n            if (this.startEvent && onEnd) {\n                onEnd(event, panInfo);\n            }\n            onSessionEnd && onSessionEnd(event, panInfo);\n        };\n        // If we have more than one touch, don't start detecting this gesture\n        if (!isPrimaryPointer(event))\n            return;\n        this.dragSnapToOrigin = dragSnapToOrigin;\n        this.handlers = handlers;\n        this.transformPagePoint = transformPagePoint;\n        this.contextWindow = contextWindow || window;\n        const info = extractEventInfo(event);\n        const initialInfo = transformPoint(info, this.transformPagePoint);\n        const { point } = initialInfo;\n        const { timestamp } = frameData;\n        this.history = [{ ...point, timestamp }];\n        const { onSessionStart } = handlers;\n        onSessionStart &&\n            onSessionStart(event, getPanInfo(initialInfo, this.history));\n        this.removeListeners = pipe(addPointerEvent(this.contextWindow, \"pointermove\", this.handlePointerMove), addPointerEvent(this.contextWindow, \"pointerup\", this.handlePointerUp), addPointerEvent(this.contextWindow, \"pointercancel\", this.handlePointerUp));\n    }\n    updateHandlers(handlers) {\n        this.handlers = handlers;\n    }\n    end() {\n        this.removeListeners && this.removeListeners();\n        cancelFrame(this.updatePoint);\n    }\n}\nfunction transformPoint(info, transformPagePoint) {\n    return transformPagePoint ? { point: transformPagePoint(info.point) } : info;\n}\nfunction subtractPoint(a, b) {\n    return { x: a.x - b.x, y: a.y - b.y };\n}\nfunction getPanInfo({ point }, history) {\n    return {\n        point,\n        delta: subtractPoint(point, lastDevicePoint(history)),\n        offset: subtractPoint(point, startDevicePoint(history)),\n        velocity: getVelocity(history, 0.1),\n    };\n}\nfunction startDevicePoint(history) {\n    return history[0];\n}\nfunction lastDevicePoint(history) {\n    return history[history.length - 1];\n}\nfunction getVelocity(history, timeDelta) {\n    if (history.length < 2) {\n        return { x: 0, y: 0 };\n    }\n    let i = history.length - 1;\n    let timestampedPoint = null;\n    const lastPoint = lastDevicePoint(history);\n    while (i >= 0) {\n        timestampedPoint = history[i];\n        if (lastPoint.timestamp - timestampedPoint.timestamp >\n            secondsToMilliseconds(timeDelta)) {\n            break;\n        }\n        i--;\n    }\n    if (!timestampedPoint) {\n        return { x: 0, y: 0 };\n    }\n    const time = millisecondsToSeconds(lastPoint.timestamp - timestampedPoint.timestamp);\n    if (time === 0) {\n        return { x: 0, y: 0 };\n    }\n    const currentVelocity = {\n        x: (lastPoint.x - timestampedPoint.x) / time,\n        y: (lastPoint.y - timestampedPoint.y) / time,\n    };\n    if (currentVelocity.x === Infinity) {\n        currentVelocity.x = 0;\n    }\n    if (currentVelocity.y === Infinity) {\n        currentVelocity.y = 0;\n    }\n    return currentVelocity;\n}\n\nexport { PanSession };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,iCAAiC;AAC9F,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,gBAAgB,QAAQ,2CAA2C;AAC5E,SAASC,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,2BAA2B;;AAEzE;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,WAAWA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAAEC,kBAAkB;IAAEC,aAAa;IAAEC,gBAAgB,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC/F;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB;AACR;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B;AACR;AACA;IACQ,IAAI,CAACN,QAAQ,GAAG,CAAC,CAAC;IAClB;AACR;AACA;IACQ,IAAI,CAACE,aAAa,GAAGK,MAAM;IAC3B,IAAI,CAACC,WAAW,GAAG,MAAM;MACrB,IAAI,EAAE,IAAI,CAACH,aAAa,IAAI,IAAI,CAACC,iBAAiB,CAAC,EAC/C;MACJ,MAAMG,IAAI,GAAGC,UAAU,CAAC,IAAI,CAACJ,iBAAiB,EAAE,IAAI,CAACK,OAAO,CAAC;MAC7D,MAAMC,YAAY,GAAG,IAAI,CAACR,UAAU,KAAK,IAAI;MAC7C;MACA;MACA;MACA,MAAMS,uBAAuB,GAAGrB,UAAU,CAACiB,IAAI,CAACK,MAAM,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,CAAC,IAAI,CAAC;MAC5E,IAAI,CAACJ,YAAY,IAAI,CAACC,uBAAuB,EACzC;MACJ,MAAM;QAAEI;MAAM,CAAC,GAAGR,IAAI;MACtB,MAAM;QAAES;MAAU,CAAC,GAAGtB,SAAS;MAC/B,IAAI,CAACe,OAAO,CAACQ,IAAI,CAAC;QAAE,GAAGF,KAAK;QAAEC;MAAU,CAAC,CAAC;MAC1C,MAAM;QAAEE,OAAO;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACrB,QAAQ;MACzC,IAAI,CAACY,YAAY,EAAE;QACfQ,OAAO,IAAIA,OAAO,CAAC,IAAI,CAACf,aAAa,EAAEI,IAAI,CAAC;QAC5C,IAAI,CAACL,UAAU,GAAG,IAAI,CAACC,aAAa;MACxC;MACAgB,MAAM,IAAIA,MAAM,CAAC,IAAI,CAAChB,aAAa,EAAEI,IAAI,CAAC;IAC9C,CAAC;IACD,IAAI,CAACa,iBAAiB,GAAG,CAACvB,KAAK,EAAEU,IAAI,KAAK;MACtC,IAAI,CAACJ,aAAa,GAAGN,KAAK;MAC1B,IAAI,CAACO,iBAAiB,GAAGiB,cAAc,CAACd,IAAI,EAAE,IAAI,CAACR,kBAAkB,CAAC;MACtE;MACAP,KAAK,CAAC8B,MAAM,CAAC,IAAI,CAAChB,WAAW,EAAE,IAAI,CAAC;IACxC,CAAC;IACD,IAAI,CAACiB,eAAe,GAAG,CAAC1B,KAAK,EAAEU,IAAI,KAAK;MACpC,IAAI,CAACiB,GAAG,CAAC,CAAC;MACV,MAAM;QAAEC,KAAK;QAAEC,YAAY;QAAEC;MAAgB,CAAC,GAAG,IAAI,CAAC7B,QAAQ;MAC9D,IAAI,IAAI,CAACG,gBAAgB,EACrB0B,eAAe,IAAIA,eAAe,CAAC,CAAC;MACxC,IAAI,EAAE,IAAI,CAACxB,aAAa,IAAI,IAAI,CAACC,iBAAiB,CAAC,EAC/C;MACJ,MAAMwB,OAAO,GAAGpB,UAAU,CAACX,KAAK,CAACgC,IAAI,KAAK,eAAe,GACnD,IAAI,CAACzB,iBAAiB,GACtBiB,cAAc,CAACd,IAAI,EAAE,IAAI,CAACR,kBAAkB,CAAC,EAAE,IAAI,CAACU,OAAO,CAAC;MAClE,IAAI,IAAI,CAACP,UAAU,IAAIuB,KAAK,EAAE;QAC1BA,KAAK,CAAC5B,KAAK,EAAE+B,OAAO,CAAC;MACzB;MACAF,YAAY,IAAIA,YAAY,CAAC7B,KAAK,EAAE+B,OAAO,CAAC;IAChD,CAAC;IACD;IACA,IAAI,CAACrC,gBAAgB,CAACM,KAAK,CAAC,EACxB;IACJ,IAAI,CAACI,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,aAAa,GAAGA,aAAa,IAAIK,MAAM;IAC5C,MAAME,IAAI,GAAGtB,gBAAgB,CAACY,KAAK,CAAC;IACpC,MAAMiC,WAAW,GAAGT,cAAc,CAACd,IAAI,EAAE,IAAI,CAACR,kBAAkB,CAAC;IACjE,MAAM;MAAEgB;IAAM,CAAC,GAAGe,WAAW;IAC7B,MAAM;MAAEd;IAAU,CAAC,GAAGtB,SAAS;IAC/B,IAAI,CAACe,OAAO,GAAG,CAAC;MAAE,GAAGM,KAAK;MAAEC;IAAU,CAAC,CAAC;IACxC,MAAM;MAAEe;IAAe,CAAC,GAAGjC,QAAQ;IACnCiC,cAAc,IACVA,cAAc,CAAClC,KAAK,EAAEW,UAAU,CAACsB,WAAW,EAAE,IAAI,CAACrB,OAAO,CAAC,CAAC;IAChE,IAAI,CAACuB,eAAe,GAAG3C,IAAI,CAACD,eAAe,CAAC,IAAI,CAACY,aAAa,EAAE,aAAa,EAAE,IAAI,CAACoB,iBAAiB,CAAC,EAAEhC,eAAe,CAAC,IAAI,CAACY,aAAa,EAAE,WAAW,EAAE,IAAI,CAACuB,eAAe,CAAC,EAAEnC,eAAe,CAAC,IAAI,CAACY,aAAa,EAAE,eAAe,EAAE,IAAI,CAACuB,eAAe,CAAC,CAAC;EAC/P;EACAU,cAAcA,CAACnC,QAAQ,EAAE;IACrB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA0B,GAAGA,CAAA,EAAG;IACF,IAAI,CAACQ,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,CAAC;IAC9CvC,WAAW,CAAC,IAAI,CAACa,WAAW,CAAC;EACjC;AACJ;AACA,SAASe,cAAcA,CAACd,IAAI,EAAER,kBAAkB,EAAE;EAC9C,OAAOA,kBAAkB,GAAG;IAAEgB,KAAK,EAAEhB,kBAAkB,CAACQ,IAAI,CAACQ,KAAK;EAAE,CAAC,GAAGR,IAAI;AAChF;AACA,SAAS2B,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAO;IAAEvB,CAAC,EAAEsB,CAAC,CAACtB,CAAC,GAAGuB,CAAC,CAACvB,CAAC;IAAEC,CAAC,EAAEqB,CAAC,CAACrB,CAAC,GAAGsB,CAAC,CAACtB;EAAE,CAAC;AACzC;AACA,SAASN,UAAUA,CAAC;EAAEO;AAAM,CAAC,EAAEN,OAAO,EAAE;EACpC,OAAO;IACHM,KAAK;IACLsB,KAAK,EAAEH,aAAa,CAACnB,KAAK,EAAEuB,eAAe,CAAC7B,OAAO,CAAC,CAAC;IACrDG,MAAM,EAAEsB,aAAa,CAACnB,KAAK,EAAEwB,gBAAgB,CAAC9B,OAAO,CAAC,CAAC;IACvD+B,QAAQ,EAAEC,WAAW,CAAChC,OAAO,EAAE,GAAG;EACtC,CAAC;AACL;AACA,SAAS8B,gBAAgBA,CAAC9B,OAAO,EAAE;EAC/B,OAAOA,OAAO,CAAC,CAAC,CAAC;AACrB;AACA,SAAS6B,eAAeA,CAAC7B,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACA,OAAO,CAACiC,MAAM,GAAG,CAAC,CAAC;AACtC;AACA,SAASD,WAAWA,CAAChC,OAAO,EAAEkC,SAAS,EAAE;EACrC,IAAIlC,OAAO,CAACiC,MAAM,GAAG,CAAC,EAAE;IACpB,OAAO;MAAE7B,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EACzB;EACA,IAAI8B,CAAC,GAAGnC,OAAO,CAACiC,MAAM,GAAG,CAAC;EAC1B,IAAIG,gBAAgB,GAAG,IAAI;EAC3B,MAAMC,SAAS,GAAGR,eAAe,CAAC7B,OAAO,CAAC;EAC1C,OAAOmC,CAAC,IAAI,CAAC,EAAE;IACXC,gBAAgB,GAAGpC,OAAO,CAACmC,CAAC,CAAC;IAC7B,IAAIE,SAAS,CAAC9B,SAAS,GAAG6B,gBAAgB,CAAC7B,SAAS,GAChD9B,qBAAqB,CAACyD,SAAS,CAAC,EAAE;MAClC;IACJ;IACAC,CAAC,EAAE;EACP;EACA,IAAI,CAACC,gBAAgB,EAAE;IACnB,OAAO;MAAEhC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EACzB;EACA,MAAMiC,IAAI,GAAG5D,qBAAqB,CAAC2D,SAAS,CAAC9B,SAAS,GAAG6B,gBAAgB,CAAC7B,SAAS,CAAC;EACpF,IAAI+B,IAAI,KAAK,CAAC,EAAE;IACZ,OAAO;MAAElC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EACzB;EACA,MAAMkC,eAAe,GAAG;IACpBnC,CAAC,EAAE,CAACiC,SAAS,CAACjC,CAAC,GAAGgC,gBAAgB,CAAChC,CAAC,IAAIkC,IAAI;IAC5CjC,CAAC,EAAE,CAACgC,SAAS,CAAChC,CAAC,GAAG+B,gBAAgB,CAAC/B,CAAC,IAAIiC;EAC5C,CAAC;EACD,IAAIC,eAAe,CAACnC,CAAC,KAAKoC,QAAQ,EAAE;IAChCD,eAAe,CAACnC,CAAC,GAAG,CAAC;EACzB;EACA,IAAImC,eAAe,CAAClC,CAAC,KAAKmC,QAAQ,EAAE;IAChCD,eAAe,CAAClC,CAAC,GAAG,CAAC;EACzB;EACA,OAAOkC,eAAe;AAC1B;AAEA,SAASrD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}