{"ast": null, "code": "import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() {}\nexport class BaseXHR extends Polling {\n  /**\n   * XHR Polling constructor.\n   *\n   * @param {Object} opts\n   * @package\n   */\n  constructor(opts) {\n    super(opts);\n    if (typeof location !== \"undefined\") {\n      const isSSL = \"https:\" === location.protocol;\n      let port = location.port;\n      // some user agents have empty `location.port`\n      if (!port) {\n        port = isSSL ? \"443\" : \"80\";\n      }\n      this.xd = typeof location !== \"undefined\" && opts.hostname !== location.hostname || port !== opts.port;\n    }\n  }\n  /**\n   * Sends data.\n   *\n   * @param {String} data to send.\n   * @param {Function} called upon flush.\n   * @private\n   */\n  doWrite(data, fn) {\n    const req = this.request({\n      method: \"POST\",\n      data: data\n    });\n    req.on(\"success\", fn);\n    req.on(\"error\", (xhrStatus, context) => {\n      this.onError(\"xhr post error\", xhrStatus, context);\n    });\n  }\n  /**\n   * Starts a poll cycle.\n   *\n   * @private\n   */\n  doPoll() {\n    const req = this.request();\n    req.on(\"data\", this.onData.bind(this));\n    req.on(\"error\", (xhrStatus, context) => {\n      this.onError(\"xhr poll error\", xhrStatus, context);\n    });\n    this.pollXhr = req;\n  }\n}\nexport class Request extends Emitter {\n  /**\n   * Request constructor\n   *\n   * @param {Object} options\n   * @package\n   */\n  constructor(createRequest, uri, opts) {\n    super();\n    this.createRequest = createRequest;\n    installTimerFunctions(this, opts);\n    this._opts = opts;\n    this._method = opts.method || \"GET\";\n    this._uri = uri;\n    this._data = undefined !== opts.data ? opts.data : null;\n    this._create();\n  }\n  /**\n   * Creates the XHR object and sends the request.\n   *\n   * @private\n   */\n  _create() {\n    var _a;\n    const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n    opts.xdomain = !!this._opts.xd;\n    const xhr = this._xhr = this.createRequest(opts);\n    try {\n      xhr.open(this._method, this._uri, true);\n      try {\n        if (this._opts.extraHeaders) {\n          // @ts-ignore\n          xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n          for (let i in this._opts.extraHeaders) {\n            if (this._opts.extraHeaders.hasOwnProperty(i)) {\n              xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n            }\n          }\n        }\n      } catch (e) {}\n      if (\"POST\" === this._method) {\n        try {\n          xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n        } catch (e) {}\n      }\n      try {\n        xhr.setRequestHeader(\"Accept\", \"*/*\");\n      } catch (e) {}\n      (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n      // ie6 check\n      if (\"withCredentials\" in xhr) {\n        xhr.withCredentials = this._opts.withCredentials;\n      }\n      if (this._opts.requestTimeout) {\n        xhr.timeout = this._opts.requestTimeout;\n      }\n      xhr.onreadystatechange = () => {\n        var _a;\n        if (xhr.readyState === 3) {\n          (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n          // @ts-ignore\n          xhr.getResponseHeader(\"set-cookie\"));\n        }\n        if (4 !== xhr.readyState) return;\n        if (200 === xhr.status || 1223 === xhr.status) {\n          this._onLoad();\n        } else {\n          // make sure the `error` event handler that's user-set\n          // does not throw in the same tick and gets caught here\n          this.setTimeoutFn(() => {\n            this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n          }, 0);\n        }\n      };\n      xhr.send(this._data);\n    } catch (e) {\n      // Need to defer since .create() is called directly from the constructor\n      // and thus the 'error' event can only be only bound *after* this exception\n      // occurs.  Therefore, also, we cannot throw here at all.\n      this.setTimeoutFn(() => {\n        this._onError(e);\n      }, 0);\n      return;\n    }\n    if (typeof document !== \"undefined\") {\n      this._index = Request.requestsCount++;\n      Request.requests[this._index] = this;\n    }\n  }\n  /**\n   * Called upon error.\n   *\n   * @private\n   */\n  _onError(err) {\n    this.emitReserved(\"error\", err, this._xhr);\n    this._cleanup(true);\n  }\n  /**\n   * Cleans up house.\n   *\n   * @private\n   */\n  _cleanup(fromError) {\n    if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n      return;\n    }\n    this._xhr.onreadystatechange = empty;\n    if (fromError) {\n      try {\n        this._xhr.abort();\n      } catch (e) {}\n    }\n    if (typeof document !== \"undefined\") {\n      delete Request.requests[this._index];\n    }\n    this._xhr = null;\n  }\n  /**\n   * Called upon load.\n   *\n   * @private\n   */\n  _onLoad() {\n    const data = this._xhr.responseText;\n    if (data !== null) {\n      this.emitReserved(\"data\", data);\n      this.emitReserved(\"success\");\n      this._cleanup();\n    }\n  }\n  /**\n   * Aborts the request.\n   *\n   * @package\n   */\n  abort() {\n    this._cleanup();\n  }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n  // @ts-ignore\n  if (typeof attachEvent === \"function\") {\n    // @ts-ignore\n    attachEvent(\"onunload\", unloadHandler);\n  } else if (typeof addEventListener === \"function\") {\n    const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\nfunction unloadHandler() {\n  for (let i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\nconst hasXHR2 = function () {\n  const xhr = newRequest({\n    xdomain: false\n  });\n  return xhr && xhr.responseType !== null;\n}();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n  constructor(opts) {\n    super(opts);\n    const forceBase64 = opts && opts.forceBase64;\n    this.supportsBinary = hasXHR2 && !forceBase64;\n  }\n  request(opts = {}) {\n    Object.assign(opts, {\n      xd: this.xd\n    }, this.opts);\n    return new Request(newRequest, this.uri(), opts);\n  }\n}\nfunction newRequest(opts) {\n  const xdomain = opts.xdomain;\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) {}\n  if (!xdomain) {\n    try {\n      return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n    } catch (e) {}\n  }\n}", "map": {"version": 3, "names": ["Polling", "Emitter", "installTimerFunctions", "pick", "globalThisShim", "globalThis", "hasCORS", "empty", "BaseXHR", "constructor", "opts", "location", "isSSL", "protocol", "port", "xd", "hostname", "doWrite", "data", "fn", "req", "request", "method", "on", "xhrStatus", "context", "onError", "doPoll", "onData", "bind", "pollXhr", "Request", "createRequest", "uri", "_opts", "_method", "_uri", "_data", "undefined", "_create", "_a", "xdomain", "xhr", "_xhr", "open", "extraHeaders", "setDisableHeaderCheck", "i", "hasOwnProperty", "setRequestHeader", "e", "cookieJar", "addCookies", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "readyState", "parseCookies", "getResponseHeader", "status", "_onLoad", "setTimeoutFn", "_onError", "send", "document", "_index", "requestsCount", "requests", "err", "emit<PERSON><PERSON><PERSON><PERSON>", "_cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "addEventListener", "terminationEvent", "hasXHR2", "newRequest", "responseType", "XHR", "forceBase64", "supportsBinary", "Object", "assign", "XMLHttpRequest", "concat", "join"], "sources": ["C:/Users/<USER>/Desktop/hamza/frontend/node_modules/engine.io-client/build/esm/transports/polling-xhr.js"], "sourcesContent": ["import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,qBAAqB,EAAEC,IAAI,QAAQ,YAAY;AACxD,SAASC,cAAc,IAAIC,UAAU,QAAQ,oBAAoB;AACjE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,KAAKA,CAAA,EAAG,CAAE;AACnB,OAAO,MAAMC,OAAO,SAASR,OAAO,CAAC;EACjC;AACJ;AACA;AACA;AACA;AACA;EACIS,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACjC,MAAMC,KAAK,GAAG,QAAQ,KAAKD,QAAQ,CAACE,QAAQ;MAC5C,IAAIC,IAAI,GAAGH,QAAQ,CAACG,IAAI;MACxB;MACA,IAAI,CAACA,IAAI,EAAE;QACPA,IAAI,GAAGF,KAAK,GAAG,KAAK,GAAG,IAAI;MAC/B;MACA,IAAI,CAACG,EAAE,GACF,OAAOJ,QAAQ,KAAK,WAAW,IAC5BD,IAAI,CAACM,QAAQ,KAAKL,QAAQ,CAACK,QAAQ,IACnCF,IAAI,KAAKJ,IAAI,CAACI,IAAI;IAC9B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIG,OAAOA,CAACC,IAAI,EAAEC,EAAE,EAAE;IACd,MAAMC,GAAG,GAAG,IAAI,CAACC,OAAO,CAAC;MACrBC,MAAM,EAAE,MAAM;MACdJ,IAAI,EAAEA;IACV,CAAC,CAAC;IACFE,GAAG,CAACG,EAAE,CAAC,SAAS,EAAEJ,EAAE,CAAC;IACrBC,GAAG,CAACG,EAAE,CAAC,OAAO,EAAE,CAACC,SAAS,EAAEC,OAAO,KAAK;MACpC,IAAI,CAACC,OAAO,CAAC,gBAAgB,EAAEF,SAAS,EAAEC,OAAO,CAAC;IACtD,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIE,MAAMA,CAAA,EAAG;IACL,MAAMP,GAAG,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IAC1BD,GAAG,CAACG,EAAE,CAAC,MAAM,EAAE,IAAI,CAACK,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtCT,GAAG,CAACG,EAAE,CAAC,OAAO,EAAE,CAACC,SAAS,EAAEC,OAAO,KAAK;MACpC,IAAI,CAACC,OAAO,CAAC,gBAAgB,EAAEF,SAAS,EAAEC,OAAO,CAAC;IACtD,CAAC,CAAC;IACF,IAAI,CAACK,OAAO,GAAGV,GAAG;EACtB;AACJ;AACA,OAAO,MAAMW,OAAO,SAAS9B,OAAO,CAAC;EACjC;AACJ;AACA;AACA;AACA;AACA;EACIQ,WAAWA,CAACuB,aAAa,EAAEC,GAAG,EAAEvB,IAAI,EAAE;IAClC,KAAK,CAAC,CAAC;IACP,IAAI,CAACsB,aAAa,GAAGA,aAAa;IAClC9B,qBAAqB,CAAC,IAAI,EAAEQ,IAAI,CAAC;IACjC,IAAI,CAACwB,KAAK,GAAGxB,IAAI;IACjB,IAAI,CAACyB,OAAO,GAAGzB,IAAI,CAACY,MAAM,IAAI,KAAK;IACnC,IAAI,CAACc,IAAI,GAAGH,GAAG;IACf,IAAI,CAACI,KAAK,GAAGC,SAAS,KAAK5B,IAAI,CAACQ,IAAI,GAAGR,IAAI,CAACQ,IAAI,GAAG,IAAI;IACvD,IAAI,CAACqB,OAAO,CAAC,CAAC;EAClB;EACA;AACJ;AACA;AACA;AACA;EACIA,OAAOA,CAAA,EAAG;IACN,IAAIC,EAAE;IACN,MAAM9B,IAAI,GAAGP,IAAI,CAAC,IAAI,CAAC+B,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,WAAW,CAAC;IAC9HxB,IAAI,CAAC+B,OAAO,GAAG,CAAC,CAAC,IAAI,CAACP,KAAK,CAACnB,EAAE;IAC9B,MAAM2B,GAAG,GAAI,IAAI,CAACC,IAAI,GAAG,IAAI,CAACX,aAAa,CAACtB,IAAI,CAAE;IAClD,IAAI;MACAgC,GAAG,CAACE,IAAI,CAAC,IAAI,CAACT,OAAO,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAC;MACvC,IAAI;QACA,IAAI,IAAI,CAACF,KAAK,CAACW,YAAY,EAAE;UACzB;UACAH,GAAG,CAACI,qBAAqB,IAAIJ,GAAG,CAACI,qBAAqB,CAAC,IAAI,CAAC;UAC5D,KAAK,IAAIC,CAAC,IAAI,IAAI,CAACb,KAAK,CAACW,YAAY,EAAE;YACnC,IAAI,IAAI,CAACX,KAAK,CAACW,YAAY,CAACG,cAAc,CAACD,CAAC,CAAC,EAAE;cAC3CL,GAAG,CAACO,gBAAgB,CAACF,CAAC,EAAE,IAAI,CAACb,KAAK,CAACW,YAAY,CAACE,CAAC,CAAC,CAAC;YACvD;UACJ;QACJ;MACJ,CAAC,CACD,OAAOG,CAAC,EAAE,CAAE;MACZ,IAAI,MAAM,KAAK,IAAI,CAACf,OAAO,EAAE;QACzB,IAAI;UACAO,GAAG,CAACO,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC;QACpE,CAAC,CACD,OAAOC,CAAC,EAAE,CAAE;MAChB;MACA,IAAI;QACAR,GAAG,CAACO,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC;MACzC,CAAC,CACD,OAAOC,CAAC,EAAE,CAAE;MACZ,CAACV,EAAE,GAAG,IAAI,CAACN,KAAK,CAACiB,SAAS,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,UAAU,CAACV,GAAG,CAAC;MACnF;MACA,IAAI,iBAAiB,IAAIA,GAAG,EAAE;QAC1BA,GAAG,CAACW,eAAe,GAAG,IAAI,CAACnB,KAAK,CAACmB,eAAe;MACpD;MACA,IAAI,IAAI,CAACnB,KAAK,CAACoB,cAAc,EAAE;QAC3BZ,GAAG,CAACa,OAAO,GAAG,IAAI,CAACrB,KAAK,CAACoB,cAAc;MAC3C;MACAZ,GAAG,CAACc,kBAAkB,GAAG,MAAM;QAC3B,IAAIhB,EAAE;QACN,IAAIE,GAAG,CAACe,UAAU,KAAK,CAAC,EAAE;UACtB,CAACjB,EAAE,GAAG,IAAI,CAACN,KAAK,CAACiB,SAAS,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkB,YAAY;UAChF;UACAhB,GAAG,CAACiB,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACxC;QACA,IAAI,CAAC,KAAKjB,GAAG,CAACe,UAAU,EACpB;QACJ,IAAI,GAAG,KAAKf,GAAG,CAACkB,MAAM,IAAI,IAAI,KAAKlB,GAAG,CAACkB,MAAM,EAAE;UAC3C,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,CAAC,MACI;UACD;UACA;UACA,IAAI,CAACC,YAAY,CAAC,MAAM;YACpB,IAAI,CAACC,QAAQ,CAAC,OAAOrB,GAAG,CAACkB,MAAM,KAAK,QAAQ,GAAGlB,GAAG,CAACkB,MAAM,GAAG,CAAC,CAAC;UAClE,CAAC,EAAE,CAAC,CAAC;QACT;MACJ,CAAC;MACDlB,GAAG,CAACsB,IAAI,CAAC,IAAI,CAAC3B,KAAK,CAAC;IACxB,CAAC,CACD,OAAOa,CAAC,EAAE;MACN;MACA;MACA;MACA,IAAI,CAACY,YAAY,CAAC,MAAM;QACpB,IAAI,CAACC,QAAQ,CAACb,CAAC,CAAC;MACpB,CAAC,EAAE,CAAC,CAAC;MACL;IACJ;IACA,IAAI,OAAOe,QAAQ,KAAK,WAAW,EAAE;MACjC,IAAI,CAACC,MAAM,GAAGnC,OAAO,CAACoC,aAAa,EAAE;MACrCpC,OAAO,CAACqC,QAAQ,CAAC,IAAI,CAACF,MAAM,CAAC,GAAG,IAAI;IACxC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIH,QAAQA,CAACM,GAAG,EAAE;IACV,IAAI,CAACC,YAAY,CAAC,OAAO,EAAED,GAAG,EAAE,IAAI,CAAC1B,IAAI,CAAC;IAC1C,IAAI,CAAC4B,QAAQ,CAAC,IAAI,CAAC;EACvB;EACA;AACJ;AACA;AACA;AACA;EACIA,QAAQA,CAACC,SAAS,EAAE;IAChB,IAAI,WAAW,KAAK,OAAO,IAAI,CAAC7B,IAAI,IAAI,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;MACxD;IACJ;IACA,IAAI,CAACA,IAAI,CAACa,kBAAkB,GAAGjD,KAAK;IACpC,IAAIiE,SAAS,EAAE;MACX,IAAI;QACA,IAAI,CAAC7B,IAAI,CAAC8B,KAAK,CAAC,CAAC;MACrB,CAAC,CACD,OAAOvB,CAAC,EAAE,CAAE;IAChB;IACA,IAAI,OAAOe,QAAQ,KAAK,WAAW,EAAE;MACjC,OAAOlC,OAAO,CAACqC,QAAQ,CAAC,IAAI,CAACF,MAAM,CAAC;IACxC;IACA,IAAI,CAACvB,IAAI,GAAG,IAAI;EACpB;EACA;AACJ;AACA;AACA;AACA;EACIkB,OAAOA,CAAA,EAAG;IACN,MAAM3C,IAAI,GAAG,IAAI,CAACyB,IAAI,CAAC+B,YAAY;IACnC,IAAIxD,IAAI,KAAK,IAAI,EAAE;MACf,IAAI,CAACoD,YAAY,CAAC,MAAM,EAAEpD,IAAI,CAAC;MAC/B,IAAI,CAACoD,YAAY,CAAC,SAAS,CAAC;MAC5B,IAAI,CAACC,QAAQ,CAAC,CAAC;IACnB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIE,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACF,QAAQ,CAAC,CAAC;EACnB;AACJ;AACAxC,OAAO,CAACoC,aAAa,GAAG,CAAC;AACzBpC,OAAO,CAACqC,QAAQ,GAAG,CAAC,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA,IAAI,OAAOH,QAAQ,KAAK,WAAW,EAAE;EACjC;EACA,IAAI,OAAOU,WAAW,KAAK,UAAU,EAAE;IACnC;IACAA,WAAW,CAAC,UAAU,EAAEC,aAAa,CAAC;EAC1C,CAAC,MACI,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;IAC7C,MAAMC,gBAAgB,GAAG,YAAY,IAAIzE,UAAU,GAAG,UAAU,GAAG,QAAQ;IAC3EwE,gBAAgB,CAACC,gBAAgB,EAAEF,aAAa,EAAE,KAAK,CAAC;EAC5D;AACJ;AACA,SAASA,aAAaA,CAAA,EAAG;EACrB,KAAK,IAAI7B,CAAC,IAAIhB,OAAO,CAACqC,QAAQ,EAAE;IAC5B,IAAIrC,OAAO,CAACqC,QAAQ,CAACpB,cAAc,CAACD,CAAC,CAAC,EAAE;MACpChB,OAAO,CAACqC,QAAQ,CAACrB,CAAC,CAAC,CAAC0B,KAAK,CAAC,CAAC;IAC/B;EACJ;AACJ;AACA,MAAMM,OAAO,GAAI,YAAY;EACzB,MAAMrC,GAAG,GAAGsC,UAAU,CAAC;IACnBvC,OAAO,EAAE;EACb,CAAC,CAAC;EACF,OAAOC,GAAG,IAAIA,GAAG,CAACuC,YAAY,KAAK,IAAI;AAC3C,CAAC,CAAE,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,GAAG,SAAS1E,OAAO,CAAC;EAC7BC,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAACA,IAAI,CAAC;IACX,MAAMyE,WAAW,GAAGzE,IAAI,IAAIA,IAAI,CAACyE,WAAW;IAC5C,IAAI,CAACC,cAAc,GAAGL,OAAO,IAAI,CAACI,WAAW;EACjD;EACA9D,OAAOA,CAACX,IAAI,GAAG,CAAC,CAAC,EAAE;IACf2E,MAAM,CAACC,MAAM,CAAC5E,IAAI,EAAE;MAAEK,EAAE,EAAE,IAAI,CAACA;IAAG,CAAC,EAAE,IAAI,CAACL,IAAI,CAAC;IAC/C,OAAO,IAAIqB,OAAO,CAACiD,UAAU,EAAE,IAAI,CAAC/C,GAAG,CAAC,CAAC,EAAEvB,IAAI,CAAC;EACpD;AACJ;AACA,SAASsE,UAAUA,CAACtE,IAAI,EAAE;EACtB,MAAM+B,OAAO,GAAG/B,IAAI,CAAC+B,OAAO;EAC5B;EACA,IAAI;IACA,IAAI,WAAW,KAAK,OAAO8C,cAAc,KAAK,CAAC9C,OAAO,IAAInC,OAAO,CAAC,EAAE;MAChE,OAAO,IAAIiF,cAAc,CAAC,CAAC;IAC/B;EACJ,CAAC,CACD,OAAOrC,CAAC,EAAE,CAAE;EACZ,IAAI,CAACT,OAAO,EAAE;IACV,IAAI;MACA,OAAO,IAAIpC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAACmF,MAAM,CAAC,QAAQ,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACrF,CAAC,CACD,OAAOvC,CAAC,EAAE,CAAE;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}