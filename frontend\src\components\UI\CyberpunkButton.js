import React, { useState } from 'react';
import styled, { keyframes } from 'styled-components';
import { motion } from 'framer-motion';
import soundManager from '../../utils/soundEffects';

const glitch = keyframes`
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
`;

const neonPulse = keyframes`
  0%, 100% {
    box-shadow: 
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px currentColor;
  }
  50% {
    box-shadow: 
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor,
      0 0 12px currentColor;
  }
`;

const scanline = keyframes`
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
`;

const ButtonContainer = styled(motion.button)`
  position: relative;
  background: ${props => {
    if (props.variant === 'primary') return 'linear-gradient(45deg, #00ffff, #8a2be2)';
    if (props.variant === 'success') return 'linear-gradient(45deg, #00ff00, #00cc00)';
    if (props.variant === 'danger') return 'linear-gradient(45deg, #ff0040, #cc0030)';
    if (props.variant === 'warning') return 'linear-gradient(45deg, #ffff00, #ffcc00)';
    return 'linear-gradient(45deg, #333333, #555555)';
  }};
  color: white;
  border: none;
  border-radius: ${props => props.rounded ? '50px' : '8px'};
  padding: ${props => {
    if (props.size === 'small') return '0.5rem 1rem';
    if (props.size === 'large') return '1.2rem 2rem';
    return '0.8rem 1.5rem';
  }};
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  font-size: ${props => {
    if (props.size === 'small') return '0.9rem';
    if (props.size === 'large') return '1.2rem';
    return '1rem';
  }};
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  overflow: hidden;
  opacity: ${props => props.disabled ? 0.6 : 1};

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    animation: ${props => props.glitch ? glitch : 'none'} 0.3s ease-in-out;
    
    ${props => props.neon && `
      animation: ${neonPulse} 1s ease-in-out infinite;
    `}
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s;
  }

  &:hover:not(:disabled)::before {
    left: 100%;
  }

  ${props => props.scanline && `
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 2px;
      height: 100%;
      background: rgba(0, 255, 255, 0.8);
      animation: ${scanline} 2s linear infinite;
    }
  `}
`;

const ButtonText = styled.span`
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;

const LoadingSpinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const CyberpunkButton = ({
  children,
  onClick,
  disabled = false,
  loading = false,
  variant = 'primary',
  size = 'medium',
  rounded = false,
  glitch = false,
  neon = false,
  scanline = false,
  soundEffect = 'click',
  icon,
  ...props
}) => {
  const [isPressed, setIsPressed] = useState(false);

  const handleClick = (e) => {
    if (disabled || loading) return;

    setIsPressed(true);
    setTimeout(() => setIsPressed(false), 150);

    // Play sound effect
    if (soundEffect) {
      soundManager.play(soundEffect);
    }

    if (onClick) {
      onClick(e);
    }
  };

  const handleMouseEnter = () => {
    if (!disabled && !loading) {
      soundManager.play('hover');
    }
  };

  return (
    <ButtonContainer
      variant={variant}
      size={size}
      rounded={rounded}
      glitch={glitch}
      neon={neon}
      scanline={scanline}
      disabled={disabled || loading}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      whileTap={{ scale: isPressed ? 0.95 : 1 }}
      whileHover={{ scale: disabled || loading ? 1 : 1.02 }}
      {...props}
    >
      <ButtonText>
        {loading && <LoadingSpinner />}
        {!loading && icon && icon}
        {children}
      </ButtonText>
    </ButtonContainer>
  );
};

export default CyberpunkButton;
