const jwt = require('jsonwebtoken');
const { executeQuery } = require('../config/database');

// Verify JWT token
const verifyToken = (req, res, next) => {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
        return res.status(401).json({ error: 'Access denied. No token provided.' });
    }

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.user = decoded;
        next();
    } catch (error) {
        res.status(400).json({ error: 'Invalid token.' });
    }
};

// Verify admin role
const verifyAdmin = async (req, res, next) => {
    try {
        const adminQuery = 'SELECT * FROM admins WHERE id = ? AND is_active = TRUE';
        const [admin] = await executeQuery(adminQuery, [req.user.id]);

        if (!admin) {
            return res.status(403).json({ error: 'Access denied. Admin privileges required.' });
        }

        req.admin = admin;
        next();
    } catch (error) {
        console.error('Admin verification error:', error);
        res.status(500).json({ error: 'Server error during admin verification.' });
    }
};

// Verify super admin role
const verifySuperAdmin = async (req, res, next) => {
    try {
        if (req.admin.role !== 'super_admin') {
            return res.status(403).json({ error: 'Access denied. Super admin privileges required.' });
        }
        next();
    } catch (error) {
        console.error('Super admin verification error:', error);
        res.status(500).json({ error: 'Server error during super admin verification.' });
    }
};

// Optional authentication (for features that work with or without login)
const optionalAuth = (req, res, next) => {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (token) {
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            req.user = decoded;
        } catch (error) {
            // Token is invalid, but we continue without user
            req.user = null;
        }
    }

    next();
};

module.exports = {
    verifyToken,
    verifyAdmin,
    verifySuperAdmin,
    optionalAuth
};
