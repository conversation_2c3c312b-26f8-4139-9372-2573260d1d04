import React, { createContext, useContext, useEffect, useState } from 'react';
import { io } from 'socket.io-client';
import toast from 'react-hot-toast';
import { useAuth } from './AuthContext';

const SocketContext = createContext();

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const [pcStatuses, setPcStatuses] = useState({});
  const [liveReservations, setLiveReservations] = useState([]);
  const { isAdmin } = useAuth();

  useEffect(() => {
    // Initialize socket connection
    const socketUrl = process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000';
    const newSocket = io(socketUrl, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
    });

    setSocket(newSocket);

    // Connection event handlers
    newSocket.on('connect', () => {
      console.log('Connected to server');
      setConnected(true);
      
      // Join admin room if user is admin
      if (isAdmin) {
        newSocket.emit('join-admin');
      }
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
      setConnected(false);
    });

    newSocket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      setConnected(false);
    });

    // PC status updates
    newSocket.on('pc-status-update', (data) => {
      console.log('PC status update:', data);
      setPcStatuses(prev => ({
        ...prev,
        [data.pcId]: data.status
      }));
      
      // Show toast notification for status changes
      toast.success(`PC ${data.pcId} is now ${data.status}`, {
        icon: '🖥️',
        duration: 3000,
      });
    });

    // New reservation notifications
    newSocket.on('new-reservation', (data) => {
      console.log('New reservation:', data);
      setLiveReservations(prev => [data.reservation, ...prev]);
      
      if (isAdmin) {
        toast.success(`New reservation for PC ${data.reservation.pc_number}`, {
          icon: '📅',
          duration: 4000,
        });
      }
    });

    // Reservation cancellation notifications
    newSocket.on('reservation-cancelled', (data) => {
      console.log('Reservation cancelled:', data);
      setLiveReservations(prev => 
        prev.filter(res => res.id !== data.reservationId)
      );
      
      if (isAdmin) {
        toast.error(`Reservation cancelled for PC ${data.pcId}`, {
          icon: '❌',
          duration: 4000,
        });
      }
    });

    // Reservation status updates
    newSocket.on('reservation-status-update', (data) => {
      console.log('Reservation status update:', data);
      setLiveReservations(prev => 
        prev.map(res => 
          res.id === data.reservationId 
            ? { ...res, status: data.status }
            : res
        )
      );
      
      if (isAdmin) {
        toast.info(`Reservation ${data.reservationId} status: ${data.status}`, {
          icon: '🔄',
          duration: 3000,
        });
      }
    });

    // Admin-specific events
    if (isAdmin) {
      newSocket.on('admin-notification', (data) => {
        toast.info(data.message, {
          icon: '🔔',
          duration: 5000,
        });
      });
    }

    // Cleanup on unmount
    return () => {
      newSocket.close();
    };
  }, [isAdmin]);

  // Function to emit events
  const emitEvent = (eventName, data) => {
    if (socket && connected) {
      socket.emit(eventName, data);
    }
  };

  // Function to join admin room
  const joinAdminRoom = () => {
    if (socket && connected) {
      socket.emit('join-admin');
    }
  };

  // Function to update PC status (admin only)
  const updatePcStatus = (pcId, status) => {
    if (socket && connected && isAdmin) {
      socket.emit('update-pc-status', { pcId, status });
    }
  };

  // Function to broadcast admin message
  const broadcastAdminMessage = (message) => {
    if (socket && connected && isAdmin) {
      socket.emit('admin-broadcast', { message });
    }
  };

  // Function to get current PC status
  const getPcStatus = (pcId) => {
    return pcStatuses[pcId] || 'available';
  };

  // Function to subscribe to specific events
  const subscribe = (eventName, callback) => {
    if (socket) {
      socket.on(eventName, callback);
      
      // Return unsubscribe function
      return () => {
        socket.off(eventName, callback);
      };
    }
  };

  const value = {
    socket,
    connected,
    pcStatuses,
    liveReservations,
    emitEvent,
    joinAdminRoom,
    updatePcStatus,
    broadcastAdminMessage,
    getPcStatus,
    subscribe
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
