import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import axios from 'axios';
import toast from 'react-hot-toast';
import { Calendar, Clock, User, Mail, Phone, CreditCard } from 'lucide-react';
import LoadingSpinner from '../components/UI/LoadingSpinner';

const FormContainer = styled.div`
  min-height: 100vh;
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const FormCard = styled(motion.div)`
  background: rgba(26, 26, 46, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 16px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);

  @media (max-width: 768px) {
    padding: 2rem;
  }
`;

const FormTitle = styled.h1`
  text-align: center;
  margin-bottom: 2rem;
  color: #00ffff;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
`;

const PCInfo = styled.div`
  background: rgba(10, 10, 10, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(0, 255, 255, 0.2);

  .pc-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #00ffff;
    margin-bottom: 0.5rem;
  }

  .pc-specs {
    color: #b0b0b0;
    font-size: 0.9rem;
  }
`;

const Form = styled.form`
  display: grid;
  gap: 1.5rem;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: ${props => props.columns || '1fr'};
  gap: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const InputGroup = styled.div`
  position: relative;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  color: #00ffff;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
`;

const InputIcon = styled.div`
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #00ffff;
  z-index: 1;
`;

const Input = styled.input`
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  background: rgba(10, 10, 10, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: #00ffff;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
  }

  &::placeholder {
    color: #666666;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  background: rgba(10, 10, 10, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: #00ffff;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
  }

  option {
    background: #1a1a2e;
    color: #ffffff;
  }
`;

const PriceDisplay = styled.div`
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  margin: 1rem 0;

  .price {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    font-weight: 700;
    color: #00ffff;
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
  }

  .duration {
    color: #b0b0b0;
    margin-top: 0.5rem;
  }
`;

const SubmitButton = styled.button`
  width: 100%;
  padding: 1.2rem;
  background: linear-gradient(45deg, #00ffff, #8a2be2);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 1rem;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 255, 0.4);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const BackButton = styled.button`
  background: none;
  border: 1px solid rgba(0, 255, 255, 0.3);
  color: #00ffff;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 2rem;

  &:hover {
    background: rgba(0, 255, 255, 0.1);
  }
`;

const ReservationForm = () => {
  const { pcId } = useParams();
  const navigate = useNavigate();
  const [pc, setPc] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  const [formData, setFormData] = useState({
    customer_name: '',
    customer_email: '',
    customer_phone: '',
    start_date: '',
    start_time: '',
    duration_hours: '1',
    notes: ''
  });

  const [totalCost, setTotalCost] = useState(0);

  useEffect(() => {
    fetchPC();
  }, [pcId]);

  useEffect(() => {
    calculateCost();
  }, [formData.duration_hours]);

  const fetchPC = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/pcs/${pcId}`);
      setPc(response.data);
      
      if (response.data.current_status !== 'available') {
        toast.error('This PC is not available for reservation');
        navigate('/');
      }
    } catch (error) {
      console.error('Error fetching PC:', error);
      toast.error('PC not found');
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const calculateCost = () => {
    const duration = parseFloat(formData.duration_hours);
    const baseRate = 15; // $15 per hour base rate
    
    // Apply discounts for longer sessions
    let rate = baseRate;
    if (duration >= 4) rate = 12;
    else if (duration >= 3) rate = 13;
    else if (duration >= 2) rate = 14;
    
    setTotalCost(duration * rate);
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const startDateTime = new Date(`${formData.start_date}T${formData.start_time}`);
      const endDateTime = new Date(startDateTime.getTime() + (parseFloat(formData.duration_hours) * 60 * 60 * 1000));

      const reservationData = {
        pc_id: parseInt(pcId),
        customer_name: formData.customer_name,
        customer_email: formData.customer_email,
        customer_phone: formData.customer_phone,
        start_time: startDateTime.toISOString(),
        end_time: endDateTime.toISOString(),
        duration_hours: parseFloat(formData.duration_hours),
        notes: formData.notes
      };

      await axios.post('/api/reservations', reservationData);
      
      toast.success('Reservation created successfully!');
      navigate('/');
    } catch (error) {
      console.error('Error creating reservation:', error);
      const message = error.response?.data?.error || 'Failed to create reservation';
      toast.error(message);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <FormContainer>
        <LoadingSpinner text="LOADING PC INFO" />
      </FormContainer>
    );
  }

  if (!pc) {
    return (
      <FormContainer>
        <FormTitle>PC not found</FormTitle>
      </FormContainer>
    );
  }

  const specs = pc.specifications || {};
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const minDate = tomorrow.toISOString().split('T')[0];

  return (
    <FormContainer>
      <BackButton onClick={() => navigate('/')}>
        ← Back to Gaming Lounge
      </BackButton>

      <FormCard
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <FormTitle>RESERVE {pc.name}</FormTitle>

        <PCInfo>
          <div className="pc-name">PC {pc.pc_number} - {pc.name}</div>
          <div className="pc-specs">
            {specs.cpu} • {specs.gpu} • {specs.ram} • {specs.storage}
          </div>
        </PCInfo>

        <Form onSubmit={handleSubmit}>
          <FormRow columns="1fr 1fr">
            <InputGroup>
              <Label>Customer Name</Label>
              <InputIcon><User size={20} /></InputIcon>
              <Input
                type="text"
                name="customer_name"
                placeholder="Your full name"
                value={formData.customer_name}
                onChange={handleChange}
                required
              />
            </InputGroup>

            <InputGroup>
              <Label>Email</Label>
              <InputIcon><Mail size={20} /></InputIcon>
              <Input
                type="email"
                name="customer_email"
                placeholder="<EMAIL>"
                value={formData.customer_email}
                onChange={handleChange}
                required
              />
            </InputGroup>
          </FormRow>

          <InputGroup>
            <Label>Phone (Optional)</Label>
            <InputIcon><Phone size={20} /></InputIcon>
            <Input
              type="tel"
              name="customer_phone"
              placeholder="+****************"
              value={formData.customer_phone}
              onChange={handleChange}
            />
          </InputGroup>

          <FormRow columns="1fr 1fr 1fr">
            <InputGroup>
              <Label>Date</Label>
              <InputIcon><Calendar size={20} /></InputIcon>
              <Input
                type="date"
                name="start_date"
                min={minDate}
                value={formData.start_date}
                onChange={handleChange}
                required
              />
            </InputGroup>

            <InputGroup>
              <Label>Start Time</Label>
              <InputIcon><Clock size={20} /></InputIcon>
              <Input
                type="time"
                name="start_time"
                value={formData.start_time}
                onChange={handleChange}
                required
              />
            </InputGroup>

            <InputGroup>
              <Label>Duration</Label>
              <InputIcon><CreditCard size={20} /></InputIcon>
              <Select
                name="duration_hours"
                value={formData.duration_hours}
                onChange={handleChange}
                required
              >
                <option value="1">1 Hour</option>
                <option value="2">2 Hours</option>
                <option value="3">3 Hours</option>
                <option value="4">4 Hours</option>
                <option value="6">6 Hours</option>
                <option value="8">8 Hours</option>
              </Select>
            </InputGroup>
          </FormRow>

          <PriceDisplay>
            <div className="price">${totalCost.toFixed(2)}</div>
            <div className="duration">{formData.duration_hours} hour{formData.duration_hours !== '1' ? 's' : ''}</div>
          </PriceDisplay>

          <SubmitButton type="submit" disabled={submitting}>
            {submitting ? 'CREATING RESERVATION...' : 'RESERVE NOW'}
          </SubmitButton>
        </Form>
      </FormCard>
    </FormContainer>
  );
};

export default ReservationForm;
