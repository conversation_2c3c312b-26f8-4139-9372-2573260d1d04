const express = require('express');
const { body, validationResult } = require('express-validator');
const { executeQuery, getConnection } = require('../config/database');
const { optionalAuth, verifyToken, verifyAdmin } = require('../middleware/auth');

const router = express.Router();

// Get all reservations (with optional filtering)
router.get('/', optionalAuth, async (req, res) => {
    try {
        const { status, date, pc_id } = req.query;
        let query = `
            SELECT 
                r.*,
                p.pc_number,
                p.name as pc_name
            FROM reservations r
            JOIN pcs p ON r.pc_id = p.id
            WHERE 1=1
        `;
        const params = [];

        if (status) {
            query += ' AND r.status = ?';
            params.push(status);
        }

        if (date) {
            query += ' AND DATE(r.start_time) = DATE(?)';
            params.push(date);
        }

        if (pc_id) {
            query += ' AND r.pc_id = ?';
            params.push(pc_id);
        }

        query += ' ORDER BY r.start_time DESC';

        const reservations = await executeQuery(query, params);
        res.json(reservations);
    } catch (error) {
        console.error('Error fetching reservations:', error);
        res.status(500).json({ error: 'Failed to fetch reservations' });
    }
});

// Create a new reservation
router.post('/',
    [
        body('pc_id').isInt({ min: 1 }).withMessage('Valid PC ID is required'),
        body('customer_name').trim().isLength({ min: 2, max: 100 })
            .withMessage('Customer name must be 2-100 characters'),
        body('customer_email').isEmail().withMessage('Valid email is required'),
        body('customer_phone').optional().isMobilePhone()
            .withMessage('Valid phone number is required'),
        body('start_time').isISO8601().withMessage('Valid start time is required'),
        body('end_time').isISO8601().withMessage('Valid end time is required'),
        body('duration_hours').isFloat({ min: 0.5, max: 12 })
            .withMessage('Duration must be between 0.5 and 12 hours')
    ],
    async (req, res) => {
        const connection = await getConnection();
        
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ errors: errors.array() });
            }

            const {
                pc_id,
                customer_name,
                customer_email,
                customer_phone,
                start_time,
                end_time,
                duration_hours,
                notes
            } = req.body;

            await connection.beginTransaction();

            // Check if PC exists and is available
            const [pc] = await connection.execute(
                'SELECT * FROM pcs WHERE id = ? AND status != "maintenance"',
                [pc_id]
            );

            if (!pc) {
                await connection.rollback();
                return res.status(404).json({ error: 'PC not found or under maintenance' });
            }

            // Check for conflicting reservations
            const [conflictingReservation] = await connection.execute(`
                SELECT id FROM reservations 
                WHERE pc_id = ? 
                    AND status IN ('confirmed', 'pending')
                    AND (
                        (start_time <= ? AND end_time > ?) OR
                        (start_time < ? AND end_time >= ?) OR
                        (start_time >= ? AND end_time <= ?)
                    )
            `, [pc_id, start_time, start_time, end_time, end_time, start_time, end_time]);

            if (conflictingReservation) {
                await connection.rollback();
                return res.status(409).json({ error: 'Time slot is already reserved' });
            }

            // Calculate total cost
            const [pricing] = await connection.execute(
                'SELECT price_per_hour FROM pricing WHERE duration_hours <= ? AND is_active = TRUE ORDER BY duration_hours DESC LIMIT 1',
                [duration_hours]
            );

            const pricePerHour = pricing ? pricing.price_per_hour : 15.00;
            const totalCost = duration_hours * pricePerHour;

            // Create reservation
            const [result] = await connection.execute(`
                INSERT INTO reservations 
                (pc_id, customer_name, customer_email, customer_phone, start_time, end_time, duration_hours, total_cost, notes, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'confirmed')
            `, [pc_id, customer_name, customer_email, customer_phone, start_time, end_time, duration_hours, totalCost, notes]);

            await connection.commit();

            // Get the created reservation with PC details
            const [newReservation] = await executeQuery(`
                SELECT 
                    r.*,
                    p.pc_number,
                    p.name as pc_name
                FROM reservations r
                JOIN pcs p ON r.pc_id = p.id
                WHERE r.id = ?
            `, [result.insertId]);

            // Broadcast new reservation to all connected clients
            req.io.emit('new-reservation', {
                reservation: newReservation,
                timestamp: new Date().toISOString()
            });

            res.status(201).json({
                message: 'Reservation created successfully',
                reservation: newReservation
            });

        } catch (error) {
            await connection.rollback();
            console.error('Error creating reservation:', error);
            res.status(500).json({ error: 'Failed to create reservation' });
        } finally {
            connection.release();
        }
    }
);

// Get specific reservation
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [reservation] = await executeQuery(`
            SELECT 
                r.*,
                p.pc_number,
                p.name as pc_name
            FROM reservations r
            JOIN pcs p ON r.pc_id = p.id
            WHERE r.id = ?
        `, [id]);

        if (!reservation) {
            return res.status(404).json({ error: 'Reservation not found' });
        }

        res.json(reservation);
    } catch (error) {
        console.error('Error fetching reservation:', error);
        res.status(500).json({ error: 'Failed to fetch reservation' });
    }
});

// Cancel reservation
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const [reservation] = await executeQuery(
            'SELECT * FROM reservations WHERE id = ? AND status IN ("pending", "confirmed")',
            [id]
        );

        if (!reservation) {
            return res.status(404).json({ error: 'Reservation not found or cannot be cancelled' });
        }

        // Check if cancellation is allowed (24 hours before start time)
        const startTime = new Date(reservation.start_time);
        const now = new Date();
        const hoursUntilStart = (startTime - now) / (1000 * 60 * 60);

        if (hoursUntilStart < 24) {
            return res.status(400).json({ 
                error: 'Reservations can only be cancelled 24 hours in advance' 
            });
        }

        await executeQuery(
            'UPDATE reservations SET status = "cancelled", updated_at = NOW() WHERE id = ?',
            [id]
        );

        // Broadcast cancellation to all connected clients
        req.io.emit('reservation-cancelled', {
            reservationId: parseInt(id),
            pcId: reservation.pc_id,
            timestamp: new Date().toISOString()
        });

        res.json({ message: 'Reservation cancelled successfully' });
    } catch (error) {
        console.error('Error cancelling reservation:', error);
        res.status(500).json({ error: 'Failed to cancel reservation' });
    }
});

module.exports = router;
