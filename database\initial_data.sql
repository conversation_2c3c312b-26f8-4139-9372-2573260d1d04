-- Initial data for Gaming Lounge Reservation System

USE gaming_lounge;

-- Insert 12 gaming PCs
INSERT INTO pcs (pc_number, name, specifications, status) VALUES
(1, 'Gaming Beast 01', '{"cpu": "Intel i9-13900K", "gpu": "RTX 4080", "ram": "32GB DDR5", "storage": "2TB NVMe SSD"}', 'available'),
(2, 'Gaming Beast 02', '{"cpu": "Intel i9-13900K", "gpu": "RTX 4080", "ram": "32GB DDR5", "storage": "2TB NVMe SSD"}', 'available'),
(3, 'Gaming Beast 03', '{"cpu": "Intel i9-13900K", "gpu": "RTX 4080", "ram": "32GB DDR5", "storage": "2TB NVMe SSD"}', 'available'),
(4, 'Gaming Beast 04', '{"cpu": "Intel i9-13900K", "gpu": "RTX 4080", "ram": "32GB DDR5", "storage": "2TB NVMe SSD"}', 'available'),
(5, 'Gaming Beast 05', '{"cpu": "Intel i9-13900K", "gpu": "RTX 4080", "ram": "32GB DDR5", "storage": "2TB NVMe SSD"}', 'available'),
(6, 'Gaming Beast 06', '{"cpu": "Intel i9-13900K", "gpu": "RTX 4080", "ram": "32GB DDR5", "storage": "2TB NVMe SSD"}', 'available'),
(7, 'Gaming Beast 07', '{"cpu": "Intel i9-13900K", "gpu": "RTX 4080", "ram": "32GB DDR5", "storage": "2TB NVMe SSD"}', 'available'),
(8, 'Gaming Beast 08', '{"cpu": "Intel i9-13900K", "gpu": "RTX 4080", "ram": "32GB DDR5", "storage": "2TB NVMe SSD"}', 'available'),
(9, 'Gaming Beast 09', '{"cpu": "Intel i9-13900K", "gpu": "RTX 4080", "ram": "32GB DDR5", "storage": "2TB NVMe SSD"}', 'available'),
(10, 'Gaming Beast 10', '{"cpu": "Intel i9-13900K", "gpu": "RTX 4080", "ram": "32GB DDR5", "storage": "2TB NVMe SSD"}', 'available'),
(11, 'Gaming Beast 11', '{"cpu": "Intel i9-13900K", "gpu": "RTX 4080", "ram": "32GB DDR5", "storage": "2TB NVMe SSD"}', 'available'),
(12, 'Gaming Beast 12', '{"cpu": "Intel i9-13900K", "gpu": "RTX 4080", "ram": "32GB DDR5", "storage": "2TB NVMe SSD"}', 'available');

-- Insert default admin user (password: admin123 - should be changed in production)
INSERT INTO admins (username, email, password_hash, role) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin');

-- Insert pricing tiers
INSERT INTO pricing (duration_hours, price_per_hour, is_active) VALUES
(1.0, 15.00, TRUE),
(2.0, 14.00, TRUE),
(3.0, 13.00, TRUE),
(4.0, 12.00, TRUE),
(6.0, 11.00, TRUE),
(8.0, 10.00, TRUE);

-- Insert system settings
INSERT INTO settings (setting_key, setting_value, description) VALUES
('lounge_name', 'CyberZone Gaming Lounge', 'Name of the gaming lounge'),
('opening_hours', '{"monday": "10:00-24:00", "tuesday": "10:00-24:00", "wednesday": "10:00-24:00", "thursday": "10:00-24:00", "friday": "10:00-02:00", "saturday": "10:00-02:00", "sunday": "12:00-22:00"}', 'Operating hours for each day'),
('max_reservation_hours', '8', 'Maximum hours a PC can be reserved'),
('advance_booking_days', '7', 'How many days in advance bookings can be made'),
('cancellation_policy', '24', 'Hours before reservation start time that cancellation is allowed'),
('contact_phone', '******-GAMING', 'Contact phone number'),
('contact_email', '<EMAIL>', 'Contact email address');

-- Insert some sample reservations for testing
INSERT INTO reservations (pc_id, customer_name, customer_email, customer_phone, start_time, end_time, duration_hours, total_cost, status) VALUES
(1, 'John Doe', '<EMAIL>', '******-0101', '2025-08-01 14:00:00', '2025-08-01 16:00:00', 2.0, 28.00, 'confirmed'),
(3, 'Jane Smith', '<EMAIL>', '******-0102', '2025-08-01 18:00:00', '2025-08-01 21:00:00', 3.0, 39.00, 'confirmed'),
(5, 'Mike Johnson', '<EMAIL>', '******-0103', '2025-08-02 10:00:00', '2025-08-02 14:00:00', 4.0, 48.00, 'pending');
