{"ast": null, "code": "import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" && typeof navigator.product === \"string\" && navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n  get name() {\n    return \"websocket\";\n  }\n  doOpen() {\n    const uri = this.uri();\n    const protocols = this.opts.protocols;\n    // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n    const opts = isReactNative ? {} : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n    if (this.opts.extraHeaders) {\n      opts.headers = this.opts.extraHeaders;\n    }\n    try {\n      this.ws = this.createSocket(uri, protocols, opts);\n    } catch (err) {\n      return this.emitReserved(\"error\", err);\n    }\n    this.ws.binaryType = this.socket.binaryType;\n    this.addEventListeners();\n  }\n  /**\n   * Adds event listeners to the socket\n   *\n   * @private\n   */\n  addEventListeners() {\n    this.ws.onopen = () => {\n      if (this.opts.autoUnref) {\n        this.ws._socket.unref();\n      }\n      this.onOpen();\n    };\n    this.ws.onclose = closeEvent => this.onClose({\n      description: \"websocket connection closed\",\n      context: closeEvent\n    });\n    this.ws.onmessage = ev => this.onData(ev.data);\n    this.ws.onerror = e => this.onError(\"websocket error\", e);\n  }\n  write(packets) {\n    this.writable = false;\n    // encodePacket efficient as it uses WS framing\n    // no need for encodePayload\n    for (let i = 0; i < packets.length; i++) {\n      const packet = packets[i];\n      const lastPacket = i === packets.length - 1;\n      encodePacket(packet, this.supportsBinary, data => {\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          this.doWrite(packet, data);\n        } catch (e) {}\n        if (lastPacket) {\n          // fake drain\n          // defer to next tick to allow Socket to clear writeBuffer\n          nextTick(() => {\n            this.writable = true;\n            this.emitReserved(\"drain\");\n          }, this.setTimeoutFn);\n        }\n      });\n    }\n  }\n  doClose() {\n    if (typeof this.ws !== \"undefined\") {\n      this.ws.onerror = () => {};\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n  /**\n   * Generates uri for connection.\n   *\n   * @private\n   */\n  uri() {\n    const schema = this.opts.secure ? \"wss\" : \"ws\";\n    const query = this.query || {};\n    // append timestamp to URI\n    if (this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = randomString();\n    }\n    // communicate binary support capabilities\n    if (!this.supportsBinary) {\n      query.b64 = 1;\n    }\n    return this.createUri(schema, query);\n  }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n  createSocket(uri, protocols, opts) {\n    return !isReactNative ? protocols ? new WebSocketCtor(uri, protocols) : new WebSocketCtor(uri) : new WebSocketCtor(uri, protocols, opts);\n  }\n  doWrite(_packet, data) {\n    this.ws.send(data);\n  }\n}", "map": {"version": 3, "names": ["Transport", "pick", "randomString", "encodePacket", "globalThisShim", "globalThis", "nextTick", "isReactNative", "navigator", "product", "toLowerCase", "BaseWS", "name", "doOpen", "uri", "protocols", "opts", "extraHeaders", "headers", "ws", "createSocket", "err", "emit<PERSON><PERSON><PERSON><PERSON>", "binaryType", "socket", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onOpen", "onclose", "closeEvent", "onClose", "description", "context", "onmessage", "ev", "onData", "data", "onerror", "e", "onError", "write", "packets", "writable", "i", "length", "packet", "lastPacket", "supportsBinary", "doWrite", "setTimeoutFn", "doClose", "close", "schema", "secure", "query", "timestampRequests", "timestampParam", "b64", "createUri", "WebSocketCtor", "WebSocket", "MozWebSocket", "WS", "_packet", "send"], "sources": ["C:/Users/<USER>/Desktop/hamza/frontend/node_modules/engine.io-client/build/esm/transports/websocket.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,IAAI,EAAEC,YAAY,QAAQ,YAAY;AAC/C,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,cAAc,IAAIC,UAAU,EAAEC,QAAQ,QAAQ,oBAAoB;AAC3E;AACA,MAAMC,aAAa,GAAG,OAAOC,SAAS,KAAK,WAAW,IAClD,OAAOA,SAAS,CAACC,OAAO,KAAK,QAAQ,IACrCD,SAAS,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,aAAa;AACrD,OAAO,MAAMC,MAAM,SAASX,SAAS,CAAC;EAClC,IAAIY,IAAIA,CAAA,EAAG;IACP,OAAO,WAAW;EACtB;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,IAAI,CAACC,IAAI,CAACD,SAAS;IACrC;IACA,MAAMC,IAAI,GAAGT,aAAa,GACpB,CAAC,CAAC,GACFN,IAAI,CAAC,IAAI,CAACe,IAAI,EAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,cAAc,EAAE,iBAAiB,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,qBAAqB,CAAC;IAC1N,IAAI,IAAI,CAACA,IAAI,CAACC,YAAY,EAAE;MACxBD,IAAI,CAACE,OAAO,GAAG,IAAI,CAACF,IAAI,CAACC,YAAY;IACzC;IACA,IAAI;MACA,IAAI,CAACE,EAAE,GAAG,IAAI,CAACC,YAAY,CAACN,GAAG,EAAEC,SAAS,EAAEC,IAAI,CAAC;IACrD,CAAC,CACD,OAAOK,GAAG,EAAE;MACR,OAAO,IAAI,CAACC,YAAY,CAAC,OAAO,EAAED,GAAG,CAAC;IAC1C;IACA,IAAI,CAACF,EAAE,CAACI,UAAU,GAAG,IAAI,CAACC,MAAM,CAACD,UAAU;IAC3C,IAAI,CAACE,iBAAiB,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIA,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACN,EAAE,CAACO,MAAM,GAAG,MAAM;MACnB,IAAI,IAAI,CAACV,IAAI,CAACW,SAAS,EAAE;QACrB,IAAI,CAACR,EAAE,CAACS,OAAO,CAACC,KAAK,CAAC,CAAC;MAC3B;MACA,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB,CAAC;IACD,IAAI,CAACX,EAAE,CAACY,OAAO,GAAIC,UAAU,IAAK,IAAI,CAACC,OAAO,CAAC;MAC3CC,WAAW,EAAE,6BAA6B;MAC1CC,OAAO,EAAEH;IACb,CAAC,CAAC;IACF,IAAI,CAACb,EAAE,CAACiB,SAAS,GAAIC,EAAE,IAAK,IAAI,CAACC,MAAM,CAACD,EAAE,CAACE,IAAI,CAAC;IAChD,IAAI,CAACpB,EAAE,CAACqB,OAAO,GAAIC,CAAC,IAAK,IAAI,CAACC,OAAO,CAAC,iBAAiB,EAAED,CAAC,CAAC;EAC/D;EACAE,KAAKA,CAACC,OAAO,EAAE;IACX,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;IACA;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,MAAME,MAAM,GAAGJ,OAAO,CAACE,CAAC,CAAC;MACzB,MAAMG,UAAU,GAAGH,CAAC,KAAKF,OAAO,CAACG,MAAM,GAAG,CAAC;MAC3C5C,YAAY,CAAC6C,MAAM,EAAE,IAAI,CAACE,cAAc,EAAGX,IAAI,IAAK;QAChD;QACA;QACA;QACA,IAAI;UACA,IAAI,CAACY,OAAO,CAACH,MAAM,EAAET,IAAI,CAAC;QAC9B,CAAC,CACD,OAAOE,CAAC,EAAE,CACV;QACA,IAAIQ,UAAU,EAAE;UACZ;UACA;UACA3C,QAAQ,CAAC,MAAM;YACX,IAAI,CAACuC,QAAQ,GAAG,IAAI;YACpB,IAAI,CAACvB,YAAY,CAAC,OAAO,CAAC;UAC9B,CAAC,EAAE,IAAI,CAAC8B,YAAY,CAAC;QACzB;MACJ,CAAC,CAAC;IACN;EACJ;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,OAAO,IAAI,CAAClC,EAAE,KAAK,WAAW,EAAE;MAChC,IAAI,CAACA,EAAE,CAACqB,OAAO,GAAG,MAAM,CAAE,CAAC;MAC3B,IAAI,CAACrB,EAAE,CAACmC,KAAK,CAAC,CAAC;MACf,IAAI,CAACnC,EAAE,GAAG,IAAI;IAClB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIL,GAAGA,CAAA,EAAG;IACF,MAAMyC,MAAM,GAAG,IAAI,CAACvC,IAAI,CAACwC,MAAM,GAAG,KAAK,GAAG,IAAI;IAC9C,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC;IAC9B;IACA,IAAI,IAAI,CAACzC,IAAI,CAAC0C,iBAAiB,EAAE;MAC7BD,KAAK,CAAC,IAAI,CAACzC,IAAI,CAAC2C,cAAc,CAAC,GAAGzD,YAAY,CAAC,CAAC;IACpD;IACA;IACA,IAAI,CAAC,IAAI,CAACgD,cAAc,EAAE;MACtBO,KAAK,CAACG,GAAG,GAAG,CAAC;IACjB;IACA,OAAO,IAAI,CAACC,SAAS,CAACN,MAAM,EAAEE,KAAK,CAAC;EACxC;AACJ;AACA,MAAMK,aAAa,GAAGzD,UAAU,CAAC0D,SAAS,IAAI1D,UAAU,CAAC2D,YAAY;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,SAAStD,MAAM,CAAC;EAC3BS,YAAYA,CAACN,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAE;IAC/B,OAAO,CAACT,aAAa,GACfQ,SAAS,GACL,IAAI+C,aAAa,CAAChD,GAAG,EAAEC,SAAS,CAAC,GACjC,IAAI+C,aAAa,CAAChD,GAAG,CAAC,GAC1B,IAAIgD,aAAa,CAAChD,GAAG,EAAEC,SAAS,EAAEC,IAAI,CAAC;EACjD;EACAmC,OAAOA,CAACe,OAAO,EAAE3B,IAAI,EAAE;IACnB,IAAI,CAACpB,EAAE,CAACgD,IAAI,CAAC5B,IAAI,CAAC;EACtB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}