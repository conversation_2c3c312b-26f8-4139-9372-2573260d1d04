# Gaming Lounge System Testing Guide

## Testing Checklist

### 1. Database and Backend Testing

#### Database Setup
- [ ] MySQL database created successfully
- [ ] Schema imported without errors
- [ ] Initial data loaded (12 PCs, admin user, pricing)
- [ ] All tables created with proper relationships

#### API Endpoints Testing

**PC Management**:
- [ ] `GET /api/pcs` - Returns all 12 PCs with status
- [ ] `GET /api/pcs/:id` - Returns specific PC details
- [ ] `PUT /api/pcs/:id/status` - Updates PC status (admin only)
- [ ] `GET /api/pcs/:id/availability` - Returns availability for date

**Reservations**:
- [ ] `POST /api/reservations` - Creates new reservation
- [ ] `GET /api/reservations` - Returns reservations list
- [ ] `GET /api/reservations/:id` - Returns specific reservation
- [ ] `DELETE /api/reservations/:id` - Cancels reservation

**Authentication**:
- [ ] `POST /api/auth/admin/login` - Admin login works
- [ ] `POST /api/auth/register` - User registration (optional)
- [ ] `POST /api/auth/login` - User login (optional)
- [ ] `GET /api/auth/verify` - Token verification

**Admin Routes**:
- [ ] `GET /api/admin/dashboard` - Returns dashboard stats
- [ ] `GET /api/admin/reservations` - Returns all reservations
- [ ] `PUT /api/admin/reservations/:id/status` - Updates reservation status

#### Backend Server Testing
```bash
# Test server startup
cd backend
npm run dev

# Check if server responds
curl http://localhost:5000/api/health

# Test PC endpoint
curl http://localhost:5000/api/pcs

# Test admin login
curl -X POST http://localhost:5000/api/auth/admin/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

### 2. Frontend Testing

#### Component Rendering
- [ ] App loads without errors
- [ ] Header displays with live clock
- [ ] PC grid shows all 12 gaming stations
- [ ] Footer renders correctly
- [ ] Loading spinners work

#### Navigation
- [ ] Home page loads
- [ ] Admin login page accessible
- [ ] Admin dashboard (after login)
- [ ] Reservation form for available PCs
- [ ] Back navigation works

#### PC Grid Functionality
- [ ] All 12 PCs display with correct information
- [ ] Status badges show correct colors
- [ ] Filter buttons work (All, Available, In Use, Reserved)
- [ ] PC cards show hover effects
- [ ] Click on available PC opens reservation form

#### Reservation System
- [ ] Reservation form opens for available PCs
- [ ] All form fields validate properly
- [ ] Date picker works (future dates only)
- [ ] Time picker functions
- [ ] Duration selection updates price
- [ ] Form submission creates reservation
- [ ] Success message displays
- [ ] Redirects back to home page

#### Admin Features
- [ ] Admin login form works
- [ ] Dashboard shows statistics
- [ ] Recent reservations display
- [ ] PC status can be updated
- [ ] Real-time updates work

### 3. Real-time Features Testing

#### Socket.io Connection
- [ ] Frontend connects to backend socket
- [ ] Connection status indicator works
- [ ] Reconnection on disconnect

#### Real-time Updates
- [ ] PC status changes broadcast to all clients
- [ ] New reservations appear immediately
- [ ] Reservation cancellations update live
- [ ] Admin notifications work
- [ ] Multiple browser tabs sync

#### Testing Real-time Features
1. Open multiple browser tabs/windows
2. Make a reservation in one tab
3. Verify it appears in other tabs immediately
4. Login as admin and change PC status
5. Verify status updates in customer view

### 4. UI/UX Testing

#### Cyberpunk Theme
- [ ] Dark theme applied consistently
- [ ] Neon colors (cyan, purple, green) used properly
- [ ] Gradient backgrounds work
- [ ] Text shadows and glows render
- [ ] Animations smooth and performant

#### Interactive Elements
- [ ] Hover effects on buttons and cards
- [ ] Click animations work
- [ ] Sound effects play (if enabled)
- [ ] Loading states display properly
- [ ] Error states handled gracefully

#### Responsive Design
- [ ] Desktop view (1920x1080)
- [ ] Tablet view (768x1024)
- [ ] Mobile view (375x667)
- [ ] Navigation adapts to screen size
- [ ] Grid layout responsive
- [ ] Forms usable on mobile

### 5. Security Testing

#### Authentication
- [ ] JWT tokens required for admin routes
- [ ] Invalid tokens rejected
- [ ] Token expiration handled
- [ ] Password hashing works

#### Input Validation
- [ ] SQL injection prevention
- [ ] XSS protection
- [ ] CORS properly configured
- [ ] Rate limiting active

#### Admin Protection
- [ ] Admin routes require authentication
- [ ] Non-admin users cannot access admin features
- [ ] Sensitive data not exposed in API

### 6. Performance Testing

#### Load Testing
- [ ] Multiple simultaneous reservations
- [ ] Many concurrent socket connections
- [ ] Database query performance
- [ ] Frontend rendering performance

#### Browser Compatibility
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### 7. Error Handling Testing

#### Network Errors
- [ ] Backend server down
- [ ] Database connection lost
- [ ] Socket disconnection
- [ ] API timeout handling

#### User Errors
- [ ] Invalid form data
- [ ] Duplicate reservations
- [ ] Past date selection
- [ ] Invalid PC selection

#### System Errors
- [ ] Database errors
- [ ] Authentication failures
- [ ] Permission denied
- [ ] Server errors

## Manual Testing Scenarios

### Scenario 1: Customer Reservation Flow
1. Visit home page
2. Browse available PCs
3. Click on an available PC
4. Fill out reservation form
5. Submit reservation
6. Verify confirmation
7. Check PC status updated

### Scenario 2: Admin Management Flow
1. Login as admin
2. View dashboard statistics
3. Check recent reservations
4. Update PC status to maintenance
5. Verify status change reflected
6. Update back to available

### Scenario 3: Real-time Updates
1. Open two browser windows
2. Make reservation in window 1
3. Verify update appears in window 2
4. Login as admin in window 2
5. Change PC status
6. Verify update in window 1

### Scenario 4: Mobile Experience
1. Open on mobile device
2. Navigate through all pages
3. Make a reservation
4. Verify responsive design
5. Test touch interactions

## Automated Testing

### Backend Tests
```bash
cd backend
npm test
```

### Frontend Tests
```bash
cd frontend
npm test
```

## Performance Benchmarks

### Expected Performance
- **Page Load**: < 3 seconds
- **API Response**: < 500ms
- **Socket Latency**: < 100ms
- **Database Queries**: < 200ms

### Monitoring
- Check browser console for errors
- Monitor network tab for failed requests
- Watch for memory leaks
- Verify smooth animations

## Deployment Testing

### Production Checklist
- [ ] Environment variables set
- [ ] Database configured
- [ ] HTTPS enabled
- [ ] CORS configured for production
- [ ] Admin credentials changed
- [ ] Backup system in place

## Bug Reporting

When reporting bugs, include:
1. Steps to reproduce
2. Expected behavior
3. Actual behavior
4. Browser/device information
5. Console error messages
6. Screenshots if applicable

## Test Results

Document test results:
- ✅ Pass
- ❌ Fail
- ⚠️ Partial/Issues

### Summary
- Total Tests: ___
- Passed: ___
- Failed: ___
- Issues Found: ___

## Next Steps

After testing:
1. Fix any identified issues
2. Optimize performance bottlenecks
3. Enhance user experience
4. Add additional features
5. Prepare for production deployment
