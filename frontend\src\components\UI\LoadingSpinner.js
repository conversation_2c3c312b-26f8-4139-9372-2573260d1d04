import React from 'react';
import styled, { keyframes } from 'styled-components';

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  gap: 2rem;
`;

const SpinnerRing = styled.div`
  width: 80px;
  height: 80px;
  border: 4px solid rgba(0, 255, 255, 0.1);
  border-top: 4px solid #00ffff;
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid rgba(138, 43, 226, 0.2);
    border-top: 2px solid #8a2be2;
    border-radius: 50%;
    animation: ${spin} 2s linear infinite reverse;
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, #00ffff, transparent);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: ${pulse} 1.5s ease-in-out infinite;
  }
`;

const LoadingText = styled.div`
  font-family: 'Orbitron', monospace;
  font-size: 1.2rem;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  animation: ${pulse} 2s ease-in-out infinite;
  text-align: center;
  letter-spacing: 2px;
`;

const LoadingDots = styled.div`
  display: flex;
  gap: 0.5rem;

  .dot {
    width: 8px;
    height: 8px;
    background: #00ffff;
    border-radius: 50%;
    animation: ${pulse} 1.4s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);

    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.4s; }
  }
`;

const LoadingSpinner = ({ text = "LOADING CYBERZONE" }) => {
  return (
    <LoadingContainer>
      <SpinnerRing />
      <LoadingText>{text}</LoadingText>
      <LoadingDots>
        <div className="dot"></div>
        <div className="dot"></div>
        <div className="dot"></div>
      </LoadingDots>
    </LoadingContainer>
  );
};

export default LoadingSpinner;
