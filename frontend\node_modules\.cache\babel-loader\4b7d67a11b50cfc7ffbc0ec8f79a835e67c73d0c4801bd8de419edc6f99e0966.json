{"ast": null, "code": "import { invariant } from '../../utils/errors.mjs';\nimport { PanSession } from '../pan/PanSession.mjs';\nimport { getGlobalLock } from './utils/lock.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, calcViewportConstraints, defaultElastic, rebaseAxisConstraints, calcOrigin } from './utils/constraints.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { percent } from '../../value/types/numbers/units.mjs';\nimport { animateMotionValue } from '../../animation/interfaces/motion-value.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nconst elementDragControls = new WeakMap();\n/**\n *\n */\n// let latestPointerEvent: PointerEvent\nclass VisualElementDragControls {\n  constructor(visualElement) {\n    // This is a reference to the global drag gesture lock, ensuring only one component\n    // can \"capture\" the drag of one or both axes.\n    // TODO: Look into moving this into pansession?\n    this.openGlobalLock = null;\n    this.isDragging = false;\n    this.currentDirection = null;\n    this.originPoint = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * The permitted boundaries of travel, in pixels.\n     */\n    this.constraints = false;\n    this.hasMutatedConstraints = false;\n    /**\n     * The per-axis resolved elastic values.\n     */\n    this.elastic = createBox();\n    this.visualElement = visualElement;\n  }\n  start(originEvent, {\n    snapToCursor = false\n  } = {}) {\n    /**\n     * Don't start dragging if this component is exiting\n     */\n    const {\n      presenceContext\n    } = this.visualElement;\n    if (presenceContext && presenceContext.isPresent === false) return;\n    const onSessionStart = event => {\n      const {\n        dragSnapToOrigin\n      } = this.getProps();\n      // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n      // the component.\n      dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n      if (snapToCursor) {\n        this.snapToCursor(extractEventInfo(event, \"page\").point);\n      }\n    };\n    const onStart = (event, info) => {\n      // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n      const {\n        drag,\n        dragPropagation,\n        onDragStart\n      } = this.getProps();\n      if (drag && !dragPropagation) {\n        if (this.openGlobalLock) this.openGlobalLock();\n        this.openGlobalLock = getGlobalLock(drag);\n        // If we don 't have the lock, don't start dragging\n        if (!this.openGlobalLock) return;\n      }\n      this.isDragging = true;\n      this.currentDirection = null;\n      this.resolveConstraints();\n      if (this.visualElement.projection) {\n        this.visualElement.projection.isAnimationBlocked = true;\n        this.visualElement.projection.target = undefined;\n      }\n      /**\n       * Record gesture origin\n       */\n      eachAxis(axis => {\n        let current = this.getAxisMotionValue(axis).get() || 0;\n        /**\n         * If the MotionValue is a percentage value convert to px\n         */\n        if (percent.test(current)) {\n          const {\n            projection\n          } = this.visualElement;\n          if (projection && projection.layout) {\n            const measuredAxis = projection.layout.layoutBox[axis];\n            if (measuredAxis) {\n              const length = calcLength(measuredAxis);\n              current = length * (parseFloat(current) / 100);\n            }\n          }\n        }\n        this.originPoint[axis] = current;\n      });\n      // Fire onDragStart event\n      if (onDragStart) {\n        frame.update(() => onDragStart(event, info), false, true);\n      }\n      const {\n        animationState\n      } = this.visualElement;\n      animationState && animationState.setActive(\"whileDrag\", true);\n    };\n    const onMove = (event, info) => {\n      // latestPointerEvent = event\n      const {\n        dragPropagation,\n        dragDirectionLock,\n        onDirectionLock,\n        onDrag\n      } = this.getProps();\n      // If we didn't successfully receive the gesture lock, early return.\n      if (!dragPropagation && !this.openGlobalLock) return;\n      const {\n        offset\n      } = info;\n      // Attempt to detect drag direction if directionLock is true\n      if (dragDirectionLock && this.currentDirection === null) {\n        this.currentDirection = getCurrentDirection(offset);\n        // If we've successfully set a direction, notify listener\n        if (this.currentDirection !== null) {\n          onDirectionLock && onDirectionLock(this.currentDirection);\n        }\n        return;\n      }\n      // Update each point with the latest position\n      this.updateAxis(\"x\", info.point, offset);\n      this.updateAxis(\"y\", info.point, offset);\n      /**\n       * Ideally we would leave the renderer to fire naturally at the end of\n       * this frame but if the element is about to change layout as the result\n       * of a re-render we want to ensure the browser can read the latest\n       * bounding box to ensure the pointer and element don't fall out of sync.\n       */\n      this.visualElement.render();\n      /**\n       * This must fire after the render call as it might trigger a state\n       * change which itself might trigger a layout update.\n       */\n      onDrag && onDrag(event, info);\n    };\n    const onSessionEnd = (event, info) => this.stop(event, info);\n    const resumeAnimation = () => eachAxis(axis => {\n      var _a;\n      return this.getAnimationState(axis) === \"paused\" && ((_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.play());\n    });\n    const {\n      dragSnapToOrigin\n    } = this.getProps();\n    this.panSession = new PanSession(originEvent, {\n      onSessionStart,\n      onStart,\n      onMove,\n      onSessionEnd,\n      resumeAnimation\n    }, {\n      transformPagePoint: this.visualElement.getTransformPagePoint(),\n      dragSnapToOrigin,\n      contextWindow: getContextWindow(this.visualElement)\n    });\n  }\n  stop(event, info) {\n    const isDragging = this.isDragging;\n    this.cancel();\n    if (!isDragging) return;\n    const {\n      velocity\n    } = info;\n    this.startAnimation(velocity);\n    const {\n      onDragEnd\n    } = this.getProps();\n    if (onDragEnd) {\n      frame.update(() => onDragEnd(event, info));\n    }\n  }\n  cancel() {\n    this.isDragging = false;\n    const {\n      projection,\n      animationState\n    } = this.visualElement;\n    if (projection) {\n      projection.isAnimationBlocked = false;\n    }\n    this.panSession && this.panSession.end();\n    this.panSession = undefined;\n    const {\n      dragPropagation\n    } = this.getProps();\n    if (!dragPropagation && this.openGlobalLock) {\n      this.openGlobalLock();\n      this.openGlobalLock = null;\n    }\n    animationState && animationState.setActive(\"whileDrag\", false);\n  }\n  updateAxis(axis, _point, offset) {\n    const {\n      drag\n    } = this.getProps();\n    // If we're not dragging this axis, do an early return.\n    if (!offset || !shouldDrag(axis, drag, this.currentDirection)) return;\n    const axisValue = this.getAxisMotionValue(axis);\n    let next = this.originPoint[axis] + offset[axis];\n    // Apply constraints\n    if (this.constraints && this.constraints[axis]) {\n      next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n    }\n    axisValue.set(next);\n  }\n  resolveConstraints() {\n    var _a;\n    const {\n      dragConstraints,\n      dragElastic\n    } = this.getProps();\n    const layout = this.visualElement.projection && !this.visualElement.projection.layout ? this.visualElement.projection.measure(false) : (_a = this.visualElement.projection) === null || _a === void 0 ? void 0 : _a.layout;\n    const prevConstraints = this.constraints;\n    if (dragConstraints && isRefObject(dragConstraints)) {\n      if (!this.constraints) {\n        this.constraints = this.resolveRefConstraints();\n      }\n    } else {\n      if (dragConstraints && layout) {\n        this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n      } else {\n        this.constraints = false;\n      }\n    }\n    this.elastic = resolveDragElastic(dragElastic);\n    /**\n     * If we're outputting to external MotionValues, we want to rebase the measured constraints\n     * from viewport-relative to component-relative.\n     */\n    if (prevConstraints !== this.constraints && layout && this.constraints && !this.hasMutatedConstraints) {\n      eachAxis(axis => {\n        if (this.getAxisMotionValue(axis)) {\n          this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n        }\n      });\n    }\n  }\n  resolveRefConstraints() {\n    const {\n      dragConstraints: constraints,\n      onMeasureDragConstraints\n    } = this.getProps();\n    if (!constraints || !isRefObject(constraints)) return false;\n    const constraintsElement = constraints.current;\n    invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n    const {\n      projection\n    } = this.visualElement;\n    // TODO\n    if (!projection || !projection.layout) return false;\n    const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n    let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n    /**\n     * If there's an onMeasureDragConstraints listener we call it and\n     * if different constraints are returned, set constraints to that\n     */\n    if (onMeasureDragConstraints) {\n      const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n      this.hasMutatedConstraints = !!userConstraints;\n      if (userConstraints) {\n        measuredConstraints = convertBoundingBoxToBox(userConstraints);\n      }\n    }\n    return measuredConstraints;\n  }\n  startAnimation(velocity) {\n    const {\n      drag,\n      dragMomentum,\n      dragElastic,\n      dragTransition,\n      dragSnapToOrigin,\n      onDragTransitionEnd\n    } = this.getProps();\n    const constraints = this.constraints || {};\n    const momentumAnimations = eachAxis(axis => {\n      if (!shouldDrag(axis, drag, this.currentDirection)) {\n        return;\n      }\n      let transition = constraints && constraints[axis] || {};\n      if (dragSnapToOrigin) transition = {\n        min: 0,\n        max: 0\n      };\n      /**\n       * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n       * of spring animations so we should look into adding a disable spring option to `inertia`.\n       * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n       * using the value of `dragElastic`.\n       */\n      const bounceStiffness = dragElastic ? 200 : 1000000;\n      const bounceDamping = dragElastic ? 40 : 10000000;\n      const inertia = {\n        type: \"inertia\",\n        velocity: dragMomentum ? velocity[axis] : 0,\n        bounceStiffness,\n        bounceDamping,\n        timeConstant: 750,\n        restDelta: 1,\n        restSpeed: 10,\n        ...dragTransition,\n        ...transition\n      };\n      // If we're not animating on an externally-provided `MotionValue` we can use the\n      // component's animation controls which will handle interactions with whileHover (etc),\n      // otherwise we just have to animate the `MotionValue` itself.\n      return this.startAxisValueAnimation(axis, inertia);\n    });\n    // Run all animations and then resolve the new drag constraints.\n    return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n  }\n  startAxisValueAnimation(axis, transition) {\n    const axisValue = this.getAxisMotionValue(axis);\n    return axisValue.start(animateMotionValue(axis, axisValue, 0, transition));\n  }\n  stopAnimation() {\n    eachAxis(axis => this.getAxisMotionValue(axis).stop());\n  }\n  pauseAnimation() {\n    eachAxis(axis => {\n      var _a;\n      return (_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.pause();\n    });\n  }\n  getAnimationState(axis) {\n    var _a;\n    return (_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.state;\n  }\n  /**\n   * Drag works differently depending on which props are provided.\n   *\n   * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n   * - Otherwise, we apply the delta to the x/y motion values.\n   */\n  getAxisMotionValue(axis) {\n    const dragKey = \"_drag\" + axis.toUpperCase();\n    const props = this.visualElement.getProps();\n    const externalMotionValue = props[dragKey];\n    return externalMotionValue ? externalMotionValue : this.visualElement.getValue(axis, (props.initial ? props.initial[axis] : undefined) || 0);\n  }\n  snapToCursor(point) {\n    eachAxis(axis => {\n      const {\n        drag\n      } = this.getProps();\n      // If we're not dragging this axis, do an early return.\n      if (!shouldDrag(axis, drag, this.currentDirection)) return;\n      const {\n        projection\n      } = this.visualElement;\n      const axisValue = this.getAxisMotionValue(axis);\n      if (projection && projection.layout) {\n        const {\n          min,\n          max\n        } = projection.layout.layoutBox[axis];\n        axisValue.set(point[axis] - mix(min, max, 0.5));\n      }\n    });\n  }\n  /**\n   * When the viewport resizes we want to check if the measured constraints\n   * have changed and, if so, reposition the element within those new constraints\n   * relative to where it was before the resize.\n   */\n  scalePositionWithinConstraints() {\n    if (!this.visualElement.current) return;\n    const {\n      drag,\n      dragConstraints\n    } = this.getProps();\n    const {\n      projection\n    } = this.visualElement;\n    if (!isRefObject(dragConstraints) || !projection || !this.constraints) return;\n    /**\n     * Stop current animations as there can be visual glitching if we try to do\n     * this mid-animation\n     */\n    this.stopAnimation();\n    /**\n     * Record the relative position of the dragged element relative to the\n     * constraints box and save as a progress value.\n     */\n    const boxProgress = {\n      x: 0,\n      y: 0\n    };\n    eachAxis(axis => {\n      const axisValue = this.getAxisMotionValue(axis);\n      if (axisValue) {\n        const latest = axisValue.get();\n        boxProgress[axis] = calcOrigin({\n          min: latest,\n          max: latest\n        }, this.constraints[axis]);\n      }\n    });\n    /**\n     * Update the layout of this element and resolve the latest drag constraints\n     */\n    const {\n      transformTemplate\n    } = this.visualElement.getProps();\n    this.visualElement.current.style.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n    projection.root && projection.root.updateScroll();\n    projection.updateLayout();\n    this.resolveConstraints();\n    /**\n     * For each axis, calculate the current progress of the layout axis\n     * within the new constraints.\n     */\n    eachAxis(axis => {\n      if (!shouldDrag(axis, drag, null)) return;\n      /**\n       * Calculate a new transform based on the previous box progress\n       */\n      const axisValue = this.getAxisMotionValue(axis);\n      const {\n        min,\n        max\n      } = this.constraints[axis];\n      axisValue.set(mix(min, max, boxProgress[axis]));\n    });\n  }\n  addListeners() {\n    if (!this.visualElement.current) return;\n    elementDragControls.set(this.visualElement, this);\n    const element = this.visualElement.current;\n    /**\n     * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n     */\n    const stopPointerListener = addPointerEvent(element, \"pointerdown\", event => {\n      const {\n        drag,\n        dragListener = true\n      } = this.getProps();\n      drag && dragListener && this.start(event);\n    });\n    const measureDragConstraints = () => {\n      const {\n        dragConstraints\n      } = this.getProps();\n      if (isRefObject(dragConstraints)) {\n        this.constraints = this.resolveRefConstraints();\n      }\n    };\n    const {\n      projection\n    } = this.visualElement;\n    const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n    if (projection && !projection.layout) {\n      projection.root && projection.root.updateScroll();\n      projection.updateLayout();\n    }\n    measureDragConstraints();\n    /**\n     * Attach a window resize listener to scale the draggable target within its defined\n     * constraints as the window resizes.\n     */\n    const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n    /**\n     * If the element's layout changes, calculate the delta and apply that to\n     * the drag gesture's origin point.\n     */\n    const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", ({\n      delta,\n      hasLayoutChanged\n    }) => {\n      if (this.isDragging && hasLayoutChanged) {\n        eachAxis(axis => {\n          const motionValue = this.getAxisMotionValue(axis);\n          if (!motionValue) return;\n          this.originPoint[axis] += delta[axis].translate;\n          motionValue.set(motionValue.get() + delta[axis].translate);\n        });\n        this.visualElement.render();\n      }\n    });\n    return () => {\n      stopResizeListener();\n      stopPointerListener();\n      stopMeasureLayoutListener();\n      stopLayoutUpdateListener && stopLayoutUpdateListener();\n    };\n  }\n  getProps() {\n    const props = this.visualElement.getProps();\n    const {\n      drag = false,\n      dragDirectionLock = false,\n      dragPropagation = false,\n      dragConstraints = false,\n      dragElastic = defaultElastic,\n      dragMomentum = true\n    } = props;\n    return {\n      ...props,\n      drag,\n      dragDirectionLock,\n      dragPropagation,\n      dragConstraints,\n      dragElastic,\n      dragMomentum\n    };\n  }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n  return (drag === true || drag === direction) && (currentDirection === null || currentDirection === direction);\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n  let direction = null;\n  if (Math.abs(offset.y) > lockThreshold) {\n    direction = \"y\";\n  } else if (Math.abs(offset.x) > lockThreshold) {\n    direction = \"x\";\n  }\n  return direction;\n}\nexport { VisualElementDragControls, elementDragControls };", "map": {"version": 3, "names": ["invariant", "PanSession", "getGlobalLock", "isRefObject", "addPointerEvent", "applyConstraints", "calcRelativeConstraints", "resolveDragElastic", "calcViewportConstraints", "defaultElastic", "rebaseAxisConstraints", "calcOrigin", "createBox", "eachAxis", "measurePageBox", "extractEventInfo", "convertBoxToBoundingBox", "convertBoundingBoxToBox", "addDomEvent", "calcLength", "mix", "percent", "animateMotionValue", "getContextWindow", "frame", "elementDragControls", "WeakMap", "VisualElementDragControls", "constructor", "visualElement", "openGlobalLock", "isDragging", "currentDirection", "originPoint", "x", "y", "constraints", "hasMutatedConstraints", "elastic", "start", "originEvent", "snapToCursor", "presenceContext", "isPresent", "onSessionStart", "event", "dragSnapToO<PERSON>in", "getProps", "pauseAnimation", "stopAnimation", "point", "onStart", "info", "drag", "dragPropagation", "onDragStart", "resolveConstraints", "projection", "isAnimationBlocked", "target", "undefined", "axis", "current", "getAxisMotionValue", "get", "test", "layout", "measuredAxis", "layoutBox", "length", "parseFloat", "update", "animationState", "setActive", "onMove", "dragDirectionLock", "onDirectionLock", "onDrag", "offset", "getCurrentDirection", "updateAxis", "render", "onSessionEnd", "stop", "resumeAnimation", "_a", "getAnimationState", "animation", "play", "panSession", "transformPagePoint", "getTransformPagePoint", "contextWindow", "cancel", "velocity", "startAnimation", "onDragEnd", "end", "_point", "shouldDrag", "axisValue", "next", "set", "dragConstraints", "dragElastic", "measure", "prevConstraints", "resolveRefConstraints", "onMeasureDragConstraints", "constraintsElement", "constraintsBox", "root", "measuredConstraints", "userConstraints", "dragMomentum", "dragTransition", "onDragTransitionEnd", "momentumAnimations", "transition", "min", "max", "bounceStiffness", "bounceDamping", "inertia", "type", "timeConstant", "restDelta", "restSpeed", "startAxisValueAnimation", "Promise", "all", "then", "pause", "state", "drag<PERSON>ey", "toUpperCase", "props", "externalMotionValue", "getValue", "initial", "scalePositionWithinConstraints", "boxProgress", "latest", "transformTemplate", "style", "transform", "updateScroll", "updateLayout", "addListeners", "element", "stopPointerListener", "dragListener", "measureDragConstraints", "stopMeasureLayoutListener", "addEventListener", "stopResizeListener", "window", "stopLayoutUpdateListener", "delta", "hasLayoutChanged", "motionValue", "translate", "direction", "lockThreshold", "Math", "abs"], "sources": ["C:/Users/<USER>/Desktop/hamza/frontend/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs"], "sourcesContent": ["import { invariant } from '../../utils/errors.mjs';\nimport { PanSession } from '../pan/PanSession.mjs';\nimport { getGlobalLock } from './utils/lock.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, calcViewportConstraints, defaultElastic, rebaseAxisConstraints, calcOrigin } from './utils/constraints.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { percent } from '../../value/types/numbers/units.mjs';\nimport { animateMotionValue } from '../../animation/interfaces/motion-value.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst elementDragControls = new WeakMap();\n/**\n *\n */\n// let latestPointerEvent: PointerEvent\nclass VisualElementDragControls {\n    constructor(visualElement) {\n        // This is a reference to the global drag gesture lock, ensuring only one component\n        // can \"capture\" the drag of one or both axes.\n        // TODO: Look into moving this into pansession?\n        this.openGlobalLock = null;\n        this.isDragging = false;\n        this.currentDirection = null;\n        this.originPoint = { x: 0, y: 0 };\n        /**\n         * The permitted boundaries of travel, in pixels.\n         */\n        this.constraints = false;\n        this.hasMutatedConstraints = false;\n        /**\n         * The per-axis resolved elastic values.\n         */\n        this.elastic = createBox();\n        this.visualElement = visualElement;\n    }\n    start(originEvent, { snapToCursor = false } = {}) {\n        /**\n         * Don't start dragging if this component is exiting\n         */\n        const { presenceContext } = this.visualElement;\n        if (presenceContext && presenceContext.isPresent === false)\n            return;\n        const onSessionStart = (event) => {\n            const { dragSnapToOrigin } = this.getProps();\n            // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n            // the component.\n            dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n            if (snapToCursor) {\n                this.snapToCursor(extractEventInfo(event, \"page\").point);\n            }\n        };\n        const onStart = (event, info) => {\n            // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n            const { drag, dragPropagation, onDragStart } = this.getProps();\n            if (drag && !dragPropagation) {\n                if (this.openGlobalLock)\n                    this.openGlobalLock();\n                this.openGlobalLock = getGlobalLock(drag);\n                // If we don 't have the lock, don't start dragging\n                if (!this.openGlobalLock)\n                    return;\n            }\n            this.isDragging = true;\n            this.currentDirection = null;\n            this.resolveConstraints();\n            if (this.visualElement.projection) {\n                this.visualElement.projection.isAnimationBlocked = true;\n                this.visualElement.projection.target = undefined;\n            }\n            /**\n             * Record gesture origin\n             */\n            eachAxis((axis) => {\n                let current = this.getAxisMotionValue(axis).get() || 0;\n                /**\n                 * If the MotionValue is a percentage value convert to px\n                 */\n                if (percent.test(current)) {\n                    const { projection } = this.visualElement;\n                    if (projection && projection.layout) {\n                        const measuredAxis = projection.layout.layoutBox[axis];\n                        if (measuredAxis) {\n                            const length = calcLength(measuredAxis);\n                            current = length * (parseFloat(current) / 100);\n                        }\n                    }\n                }\n                this.originPoint[axis] = current;\n            });\n            // Fire onDragStart event\n            if (onDragStart) {\n                frame.update(() => onDragStart(event, info), false, true);\n            }\n            const { animationState } = this.visualElement;\n            animationState && animationState.setActive(\"whileDrag\", true);\n        };\n        const onMove = (event, info) => {\n            // latestPointerEvent = event\n            const { dragPropagation, dragDirectionLock, onDirectionLock, onDrag, } = this.getProps();\n            // If we didn't successfully receive the gesture lock, early return.\n            if (!dragPropagation && !this.openGlobalLock)\n                return;\n            const { offset } = info;\n            // Attempt to detect drag direction if directionLock is true\n            if (dragDirectionLock && this.currentDirection === null) {\n                this.currentDirection = getCurrentDirection(offset);\n                // If we've successfully set a direction, notify listener\n                if (this.currentDirection !== null) {\n                    onDirectionLock && onDirectionLock(this.currentDirection);\n                }\n                return;\n            }\n            // Update each point with the latest position\n            this.updateAxis(\"x\", info.point, offset);\n            this.updateAxis(\"y\", info.point, offset);\n            /**\n             * Ideally we would leave the renderer to fire naturally at the end of\n             * this frame but if the element is about to change layout as the result\n             * of a re-render we want to ensure the browser can read the latest\n             * bounding box to ensure the pointer and element don't fall out of sync.\n             */\n            this.visualElement.render();\n            /**\n             * This must fire after the render call as it might trigger a state\n             * change which itself might trigger a layout update.\n             */\n            onDrag && onDrag(event, info);\n        };\n        const onSessionEnd = (event, info) => this.stop(event, info);\n        const resumeAnimation = () => eachAxis((axis) => {\n            var _a;\n            return this.getAnimationState(axis) === \"paused\" &&\n                ((_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.play());\n        });\n        const { dragSnapToOrigin } = this.getProps();\n        this.panSession = new PanSession(originEvent, {\n            onSessionStart,\n            onStart,\n            onMove,\n            onSessionEnd,\n            resumeAnimation,\n        }, {\n            transformPagePoint: this.visualElement.getTransformPagePoint(),\n            dragSnapToOrigin,\n            contextWindow: getContextWindow(this.visualElement),\n        });\n    }\n    stop(event, info) {\n        const isDragging = this.isDragging;\n        this.cancel();\n        if (!isDragging)\n            return;\n        const { velocity } = info;\n        this.startAnimation(velocity);\n        const { onDragEnd } = this.getProps();\n        if (onDragEnd) {\n            frame.update(() => onDragEnd(event, info));\n        }\n    }\n    cancel() {\n        this.isDragging = false;\n        const { projection, animationState } = this.visualElement;\n        if (projection) {\n            projection.isAnimationBlocked = false;\n        }\n        this.panSession && this.panSession.end();\n        this.panSession = undefined;\n        const { dragPropagation } = this.getProps();\n        if (!dragPropagation && this.openGlobalLock) {\n            this.openGlobalLock();\n            this.openGlobalLock = null;\n        }\n        animationState && animationState.setActive(\"whileDrag\", false);\n    }\n    updateAxis(axis, _point, offset) {\n        const { drag } = this.getProps();\n        // If we're not dragging this axis, do an early return.\n        if (!offset || !shouldDrag(axis, drag, this.currentDirection))\n            return;\n        const axisValue = this.getAxisMotionValue(axis);\n        let next = this.originPoint[axis] + offset[axis];\n        // Apply constraints\n        if (this.constraints && this.constraints[axis]) {\n            next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n        }\n        axisValue.set(next);\n    }\n    resolveConstraints() {\n        var _a;\n        const { dragConstraints, dragElastic } = this.getProps();\n        const layout = this.visualElement.projection &&\n            !this.visualElement.projection.layout\n            ? this.visualElement.projection.measure(false)\n            : (_a = this.visualElement.projection) === null || _a === void 0 ? void 0 : _a.layout;\n        const prevConstraints = this.constraints;\n        if (dragConstraints && isRefObject(dragConstraints)) {\n            if (!this.constraints) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        }\n        else {\n            if (dragConstraints && layout) {\n                this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n            }\n            else {\n                this.constraints = false;\n            }\n        }\n        this.elastic = resolveDragElastic(dragElastic);\n        /**\n         * If we're outputting to external MotionValues, we want to rebase the measured constraints\n         * from viewport-relative to component-relative.\n         */\n        if (prevConstraints !== this.constraints &&\n            layout &&\n            this.constraints &&\n            !this.hasMutatedConstraints) {\n            eachAxis((axis) => {\n                if (this.getAxisMotionValue(axis)) {\n                    this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n                }\n            });\n        }\n    }\n    resolveRefConstraints() {\n        const { dragConstraints: constraints, onMeasureDragConstraints } = this.getProps();\n        if (!constraints || !isRefObject(constraints))\n            return false;\n        const constraintsElement = constraints.current;\n        invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n        const { projection } = this.visualElement;\n        // TODO\n        if (!projection || !projection.layout)\n            return false;\n        const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n        let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n        /**\n         * If there's an onMeasureDragConstraints listener we call it and\n         * if different constraints are returned, set constraints to that\n         */\n        if (onMeasureDragConstraints) {\n            const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n            this.hasMutatedConstraints = !!userConstraints;\n            if (userConstraints) {\n                measuredConstraints = convertBoundingBoxToBox(userConstraints);\n            }\n        }\n        return measuredConstraints;\n    }\n    startAnimation(velocity) {\n        const { drag, dragMomentum, dragElastic, dragTransition, dragSnapToOrigin, onDragTransitionEnd, } = this.getProps();\n        const constraints = this.constraints || {};\n        const momentumAnimations = eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, this.currentDirection)) {\n                return;\n            }\n            let transition = (constraints && constraints[axis]) || {};\n            if (dragSnapToOrigin)\n                transition = { min: 0, max: 0 };\n            /**\n             * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n             * of spring animations so we should look into adding a disable spring option to `inertia`.\n             * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n             * using the value of `dragElastic`.\n             */\n            const bounceStiffness = dragElastic ? 200 : 1000000;\n            const bounceDamping = dragElastic ? 40 : 10000000;\n            const inertia = {\n                type: \"inertia\",\n                velocity: dragMomentum ? velocity[axis] : 0,\n                bounceStiffness,\n                bounceDamping,\n                timeConstant: 750,\n                restDelta: 1,\n                restSpeed: 10,\n                ...dragTransition,\n                ...transition,\n            };\n            // If we're not animating on an externally-provided `MotionValue` we can use the\n            // component's animation controls which will handle interactions with whileHover (etc),\n            // otherwise we just have to animate the `MotionValue` itself.\n            return this.startAxisValueAnimation(axis, inertia);\n        });\n        // Run all animations and then resolve the new drag constraints.\n        return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n    }\n    startAxisValueAnimation(axis, transition) {\n        const axisValue = this.getAxisMotionValue(axis);\n        return axisValue.start(animateMotionValue(axis, axisValue, 0, transition));\n    }\n    stopAnimation() {\n        eachAxis((axis) => this.getAxisMotionValue(axis).stop());\n    }\n    pauseAnimation() {\n        eachAxis((axis) => { var _a; return (_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.pause(); });\n    }\n    getAnimationState(axis) {\n        var _a;\n        return (_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.state;\n    }\n    /**\n     * Drag works differently depending on which props are provided.\n     *\n     * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n     * - Otherwise, we apply the delta to the x/y motion values.\n     */\n    getAxisMotionValue(axis) {\n        const dragKey = \"_drag\" + axis.toUpperCase();\n        const props = this.visualElement.getProps();\n        const externalMotionValue = props[dragKey];\n        return externalMotionValue\n            ? externalMotionValue\n            : this.visualElement.getValue(axis, (props.initial ? props.initial[axis] : undefined) || 0);\n    }\n    snapToCursor(point) {\n        eachAxis((axis) => {\n            const { drag } = this.getProps();\n            // If we're not dragging this axis, do an early return.\n            if (!shouldDrag(axis, drag, this.currentDirection))\n                return;\n            const { projection } = this.visualElement;\n            const axisValue = this.getAxisMotionValue(axis);\n            if (projection && projection.layout) {\n                const { min, max } = projection.layout.layoutBox[axis];\n                axisValue.set(point[axis] - mix(min, max, 0.5));\n            }\n        });\n    }\n    /**\n     * When the viewport resizes we want to check if the measured constraints\n     * have changed and, if so, reposition the element within those new constraints\n     * relative to where it was before the resize.\n     */\n    scalePositionWithinConstraints() {\n        if (!this.visualElement.current)\n            return;\n        const { drag, dragConstraints } = this.getProps();\n        const { projection } = this.visualElement;\n        if (!isRefObject(dragConstraints) || !projection || !this.constraints)\n            return;\n        /**\n         * Stop current animations as there can be visual glitching if we try to do\n         * this mid-animation\n         */\n        this.stopAnimation();\n        /**\n         * Record the relative position of the dragged element relative to the\n         * constraints box and save as a progress value.\n         */\n        const boxProgress = { x: 0, y: 0 };\n        eachAxis((axis) => {\n            const axisValue = this.getAxisMotionValue(axis);\n            if (axisValue) {\n                const latest = axisValue.get();\n                boxProgress[axis] = calcOrigin({ min: latest, max: latest }, this.constraints[axis]);\n            }\n        });\n        /**\n         * Update the layout of this element and resolve the latest drag constraints\n         */\n        const { transformTemplate } = this.visualElement.getProps();\n        this.visualElement.current.style.transform = transformTemplate\n            ? transformTemplate({}, \"\")\n            : \"none\";\n        projection.root && projection.root.updateScroll();\n        projection.updateLayout();\n        this.resolveConstraints();\n        /**\n         * For each axis, calculate the current progress of the layout axis\n         * within the new constraints.\n         */\n        eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, null))\n                return;\n            /**\n             * Calculate a new transform based on the previous box progress\n             */\n            const axisValue = this.getAxisMotionValue(axis);\n            const { min, max } = this.constraints[axis];\n            axisValue.set(mix(min, max, boxProgress[axis]));\n        });\n    }\n    addListeners() {\n        if (!this.visualElement.current)\n            return;\n        elementDragControls.set(this.visualElement, this);\n        const element = this.visualElement.current;\n        /**\n         * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n         */\n        const stopPointerListener = addPointerEvent(element, \"pointerdown\", (event) => {\n            const { drag, dragListener = true } = this.getProps();\n            drag && dragListener && this.start(event);\n        });\n        const measureDragConstraints = () => {\n            const { dragConstraints } = this.getProps();\n            if (isRefObject(dragConstraints)) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        };\n        const { projection } = this.visualElement;\n        const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n        if (projection && !projection.layout) {\n            projection.root && projection.root.updateScroll();\n            projection.updateLayout();\n        }\n        measureDragConstraints();\n        /**\n         * Attach a window resize listener to scale the draggable target within its defined\n         * constraints as the window resizes.\n         */\n        const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n        /**\n         * If the element's layout changes, calculate the delta and apply that to\n         * the drag gesture's origin point.\n         */\n        const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", (({ delta, hasLayoutChanged }) => {\n            if (this.isDragging && hasLayoutChanged) {\n                eachAxis((axis) => {\n                    const motionValue = this.getAxisMotionValue(axis);\n                    if (!motionValue)\n                        return;\n                    this.originPoint[axis] += delta[axis].translate;\n                    motionValue.set(motionValue.get() + delta[axis].translate);\n                });\n                this.visualElement.render();\n            }\n        }));\n        return () => {\n            stopResizeListener();\n            stopPointerListener();\n            stopMeasureLayoutListener();\n            stopLayoutUpdateListener && stopLayoutUpdateListener();\n        };\n    }\n    getProps() {\n        const props = this.visualElement.getProps();\n        const { drag = false, dragDirectionLock = false, dragPropagation = false, dragConstraints = false, dragElastic = defaultElastic, dragMomentum = true, } = props;\n        return {\n            ...props,\n            drag,\n            dragDirectionLock,\n            dragPropagation,\n            dragConstraints,\n            dragElastic,\n            dragMomentum,\n        };\n    }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n    return ((drag === true || drag === direction) &&\n        (currentDirection === null || currentDirection === direction));\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n    let direction = null;\n    if (Math.abs(offset.y) > lockThreshold) {\n        direction = \"y\";\n    }\n    else if (Math.abs(offset.x) > lockThreshold) {\n        direction = \"x\";\n    }\n    return direction;\n}\n\nexport { VisualElementDragControls, elementDragControls };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,gBAAgB,EAAEC,uBAAuB,EAAEC,kBAAkB,EAAEC,uBAAuB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,UAAU,QAAQ,yBAAyB;AACnL,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,QAAQ,QAAQ,sCAAsC;AAC/D,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,uBAAuB,EAAEC,uBAAuB,QAAQ,0CAA0C;AAC3G,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,GAAG,QAAQ,qBAAqB;AACzC,SAASC,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,kBAAkB,QAAQ,6CAA6C;AAChF,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,KAAK,QAAQ,2BAA2B;AAEjD,MAAMC,mBAAmB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAACC,aAAa,EAAE;IACvB;IACA;IACA;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACjC;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG1B,SAAS,CAAC,CAAC;IAC1B,IAAI,CAACiB,aAAa,GAAGA,aAAa;EACtC;EACAU,KAAKA,CAACC,WAAW,EAAE;IAAEC,YAAY,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC9C;AACR;AACA;IACQ,MAAM;MAAEC;IAAgB,CAAC,GAAG,IAAI,CAACb,aAAa;IAC9C,IAAIa,eAAe,IAAIA,eAAe,CAACC,SAAS,KAAK,KAAK,EACtD;IACJ,MAAMC,cAAc,GAAIC,KAAK,IAAK;MAC9B,MAAM;QAAEC;MAAiB,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC5C;MACA;MACAD,gBAAgB,GAAG,IAAI,CAACE,cAAc,CAAC,CAAC,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;MAC/D,IAAIR,YAAY,EAAE;QACd,IAAI,CAACA,YAAY,CAAC1B,gBAAgB,CAAC8B,KAAK,EAAE,MAAM,CAAC,CAACK,KAAK,CAAC;MAC5D;IACJ,CAAC;IACD,MAAMC,OAAO,GAAGA,CAACN,KAAK,EAAEO,IAAI,KAAK;MAC7B;MACA,MAAM;QAAEC,IAAI;QAAEC,eAAe;QAAEC;MAAY,CAAC,GAAG,IAAI,CAACR,QAAQ,CAAC,CAAC;MAC9D,IAAIM,IAAI,IAAI,CAACC,eAAe,EAAE;QAC1B,IAAI,IAAI,CAACxB,cAAc,EACnB,IAAI,CAACA,cAAc,CAAC,CAAC;QACzB,IAAI,CAACA,cAAc,GAAG5B,aAAa,CAACmD,IAAI,CAAC;QACzC;QACA,IAAI,CAAC,IAAI,CAACvB,cAAc,EACpB;MACR;MACA,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACwB,kBAAkB,CAAC,CAAC;MACzB,IAAI,IAAI,CAAC3B,aAAa,CAAC4B,UAAU,EAAE;QAC/B,IAAI,CAAC5B,aAAa,CAAC4B,UAAU,CAACC,kBAAkB,GAAG,IAAI;QACvD,IAAI,CAAC7B,aAAa,CAAC4B,UAAU,CAACE,MAAM,GAAGC,SAAS;MACpD;MACA;AACZ;AACA;MACY/C,QAAQ,CAAEgD,IAAI,IAAK;QACf,IAAIC,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAACF,IAAI,CAAC,CAACG,GAAG,CAAC,CAAC,IAAI,CAAC;QACtD;AAChB;AACA;QACgB,IAAI3C,OAAO,CAAC4C,IAAI,CAACH,OAAO,CAAC,EAAE;UACvB,MAAM;YAAEL;UAAW,CAAC,GAAG,IAAI,CAAC5B,aAAa;UACzC,IAAI4B,UAAU,IAAIA,UAAU,CAACS,MAAM,EAAE;YACjC,MAAMC,YAAY,GAAGV,UAAU,CAACS,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC;YACtD,IAAIM,YAAY,EAAE;cACd,MAAME,MAAM,GAAGlD,UAAU,CAACgD,YAAY,CAAC;cACvCL,OAAO,GAAGO,MAAM,IAAIC,UAAU,CAACR,OAAO,CAAC,GAAG,GAAG,CAAC;YAClD;UACJ;QACJ;QACA,IAAI,CAAC7B,WAAW,CAAC4B,IAAI,CAAC,GAAGC,OAAO;MACpC,CAAC,CAAC;MACF;MACA,IAAIP,WAAW,EAAE;QACb/B,KAAK,CAAC+C,MAAM,CAAC,MAAMhB,WAAW,CAACV,KAAK,EAAEO,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAC7D;MACA,MAAM;QAAEoB;MAAe,CAAC,GAAG,IAAI,CAAC3C,aAAa;MAC7C2C,cAAc,IAAIA,cAAc,CAACC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC;IACjE,CAAC;IACD,MAAMC,MAAM,GAAGA,CAAC7B,KAAK,EAAEO,IAAI,KAAK;MAC5B;MACA,MAAM;QAAEE,eAAe;QAAEqB,iBAAiB;QAAEC,eAAe;QAAEC;MAAQ,CAAC,GAAG,IAAI,CAAC9B,QAAQ,CAAC,CAAC;MACxF;MACA,IAAI,CAACO,eAAe,IAAI,CAAC,IAAI,CAACxB,cAAc,EACxC;MACJ,MAAM;QAAEgD;MAAO,CAAC,GAAG1B,IAAI;MACvB;MACA,IAAIuB,iBAAiB,IAAI,IAAI,CAAC3C,gBAAgB,KAAK,IAAI,EAAE;QACrD,IAAI,CAACA,gBAAgB,GAAG+C,mBAAmB,CAACD,MAAM,CAAC;QACnD;QACA,IAAI,IAAI,CAAC9C,gBAAgB,KAAK,IAAI,EAAE;UAChC4C,eAAe,IAAIA,eAAe,CAAC,IAAI,CAAC5C,gBAAgB,CAAC;QAC7D;QACA;MACJ;MACA;MACA,IAAI,CAACgD,UAAU,CAAC,GAAG,EAAE5B,IAAI,CAACF,KAAK,EAAE4B,MAAM,CAAC;MACxC,IAAI,CAACE,UAAU,CAAC,GAAG,EAAE5B,IAAI,CAACF,KAAK,EAAE4B,MAAM,CAAC;MACxC;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACjD,aAAa,CAACoD,MAAM,CAAC,CAAC;MAC3B;AACZ;AACA;AACA;MACYJ,MAAM,IAAIA,MAAM,CAAChC,KAAK,EAAEO,IAAI,CAAC;IACjC,CAAC;IACD,MAAM8B,YAAY,GAAGA,CAACrC,KAAK,EAAEO,IAAI,KAAK,IAAI,CAAC+B,IAAI,CAACtC,KAAK,EAAEO,IAAI,CAAC;IAC5D,MAAMgC,eAAe,GAAGA,CAAA,KAAMvE,QAAQ,CAAEgD,IAAI,IAAK;MAC7C,IAAIwB,EAAE;MACN,OAAO,IAAI,CAACC,iBAAiB,CAACzB,IAAI,CAAC,KAAK,QAAQ,KAC3C,CAACwB,EAAE,GAAG,IAAI,CAACtB,kBAAkB,CAACF,IAAI,CAAC,CAAC0B,SAAS,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAAC,CAAC,CAAC;IACvG,CAAC,CAAC;IACF,MAAM;MAAE1C;IAAiB,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC5C,IAAI,CAAC0C,UAAU,GAAG,IAAIxF,UAAU,CAACuC,WAAW,EAAE;MAC1CI,cAAc;MACdO,OAAO;MACPuB,MAAM;MACNQ,YAAY;MACZE;IACJ,CAAC,EAAE;MACCM,kBAAkB,EAAE,IAAI,CAAC7D,aAAa,CAAC8D,qBAAqB,CAAC,CAAC;MAC9D7C,gBAAgB;MAChB8C,aAAa,EAAErE,gBAAgB,CAAC,IAAI,CAACM,aAAa;IACtD,CAAC,CAAC;EACN;EACAsD,IAAIA,CAACtC,KAAK,EAAEO,IAAI,EAAE;IACd,MAAMrB,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC8D,MAAM,CAAC,CAAC;IACb,IAAI,CAAC9D,UAAU,EACX;IACJ,MAAM;MAAE+D;IAAS,CAAC,GAAG1C,IAAI;IACzB,IAAI,CAAC2C,cAAc,CAACD,QAAQ,CAAC;IAC7B,MAAM;MAAEE;IAAU,CAAC,GAAG,IAAI,CAACjD,QAAQ,CAAC,CAAC;IACrC,IAAIiD,SAAS,EAAE;MACXxE,KAAK,CAAC+C,MAAM,CAAC,MAAMyB,SAAS,CAACnD,KAAK,EAAEO,IAAI,CAAC,CAAC;IAC9C;EACJ;EACAyC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC9D,UAAU,GAAG,KAAK;IACvB,MAAM;MAAE0B,UAAU;MAAEe;IAAe,CAAC,GAAG,IAAI,CAAC3C,aAAa;IACzD,IAAI4B,UAAU,EAAE;MACZA,UAAU,CAACC,kBAAkB,GAAG,KAAK;IACzC;IACA,IAAI,CAAC+B,UAAU,IAAI,IAAI,CAACA,UAAU,CAACQ,GAAG,CAAC,CAAC;IACxC,IAAI,CAACR,UAAU,GAAG7B,SAAS;IAC3B,MAAM;MAAEN;IAAgB,CAAC,GAAG,IAAI,CAACP,QAAQ,CAAC,CAAC;IAC3C,IAAI,CAACO,eAAe,IAAI,IAAI,CAACxB,cAAc,EAAE;MACzC,IAAI,CAACA,cAAc,CAAC,CAAC;MACrB,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;IACA0C,cAAc,IAAIA,cAAc,CAACC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC;EAClE;EACAO,UAAUA,CAACnB,IAAI,EAAEqC,MAAM,EAAEpB,MAAM,EAAE;IAC7B,MAAM;MAAEzB;IAAK,CAAC,GAAG,IAAI,CAACN,QAAQ,CAAC,CAAC;IAChC;IACA,IAAI,CAAC+B,MAAM,IAAI,CAACqB,UAAU,CAACtC,IAAI,EAAER,IAAI,EAAE,IAAI,CAACrB,gBAAgB,CAAC,EACzD;IACJ,MAAMoE,SAAS,GAAG,IAAI,CAACrC,kBAAkB,CAACF,IAAI,CAAC;IAC/C,IAAIwC,IAAI,GAAG,IAAI,CAACpE,WAAW,CAAC4B,IAAI,CAAC,GAAGiB,MAAM,CAACjB,IAAI,CAAC;IAChD;IACA,IAAI,IAAI,CAACzB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACyB,IAAI,CAAC,EAAE;MAC5CwC,IAAI,GAAGhG,gBAAgB,CAACgG,IAAI,EAAE,IAAI,CAACjE,WAAW,CAACyB,IAAI,CAAC,EAAE,IAAI,CAACvB,OAAO,CAACuB,IAAI,CAAC,CAAC;IAC7E;IACAuC,SAAS,CAACE,GAAG,CAACD,IAAI,CAAC;EACvB;EACA7C,kBAAkBA,CAAA,EAAG;IACjB,IAAI6B,EAAE;IACN,MAAM;MAAEkB,eAAe;MAAEC;IAAY,CAAC,GAAG,IAAI,CAACzD,QAAQ,CAAC,CAAC;IACxD,MAAMmB,MAAM,GAAG,IAAI,CAACrC,aAAa,CAAC4B,UAAU,IACxC,CAAC,IAAI,CAAC5B,aAAa,CAAC4B,UAAU,CAACS,MAAM,GACnC,IAAI,CAACrC,aAAa,CAAC4B,UAAU,CAACgD,OAAO,CAAC,KAAK,CAAC,GAC5C,CAACpB,EAAE,GAAG,IAAI,CAACxD,aAAa,CAAC4B,UAAU,MAAM,IAAI,IAAI4B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACnB,MAAM;IACzF,MAAMwC,eAAe,GAAG,IAAI,CAACtE,WAAW;IACxC,IAAImE,eAAe,IAAIpG,WAAW,CAACoG,eAAe,CAAC,EAAE;MACjD,IAAI,CAAC,IAAI,CAACnE,WAAW,EAAE;QACnB,IAAI,CAACA,WAAW,GAAG,IAAI,CAACuE,qBAAqB,CAAC,CAAC;MACnD;IACJ,CAAC,MACI;MACD,IAAIJ,eAAe,IAAIrC,MAAM,EAAE;QAC3B,IAAI,CAAC9B,WAAW,GAAG9B,uBAAuB,CAAC4D,MAAM,CAACE,SAAS,EAAEmC,eAAe,CAAC;MACjF,CAAC,MACI;QACD,IAAI,CAACnE,WAAW,GAAG,KAAK;MAC5B;IACJ;IACA,IAAI,CAACE,OAAO,GAAG/B,kBAAkB,CAACiG,WAAW,CAAC;IAC9C;AACR;AACA;AACA;IACQ,IAAIE,eAAe,KAAK,IAAI,CAACtE,WAAW,IACpC8B,MAAM,IACN,IAAI,CAAC9B,WAAW,IAChB,CAAC,IAAI,CAACC,qBAAqB,EAAE;MAC7BxB,QAAQ,CAAEgD,IAAI,IAAK;QACf,IAAI,IAAI,CAACE,kBAAkB,CAACF,IAAI,CAAC,EAAE;UAC/B,IAAI,CAACzB,WAAW,CAACyB,IAAI,CAAC,GAAGnD,qBAAqB,CAACwD,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC,EAAE,IAAI,CAACzB,WAAW,CAACyB,IAAI,CAAC,CAAC;QAClG;MACJ,CAAC,CAAC;IACN;EACJ;EACA8C,qBAAqBA,CAAA,EAAG;IACpB,MAAM;MAAEJ,eAAe,EAAEnE,WAAW;MAAEwE;IAAyB,CAAC,GAAG,IAAI,CAAC7D,QAAQ,CAAC,CAAC;IAClF,IAAI,CAACX,WAAW,IAAI,CAACjC,WAAW,CAACiC,WAAW,CAAC,EACzC,OAAO,KAAK;IAChB,MAAMyE,kBAAkB,GAAGzE,WAAW,CAAC0B,OAAO;IAC9C9D,SAAS,CAAC6G,kBAAkB,KAAK,IAAI,EAAE,wGAAwG,CAAC;IAChJ,MAAM;MAAEpD;IAAW,CAAC,GAAG,IAAI,CAAC5B,aAAa;IACzC;IACA,IAAI,CAAC4B,UAAU,IAAI,CAACA,UAAU,CAACS,MAAM,EACjC,OAAO,KAAK;IAChB,MAAM4C,cAAc,GAAGhG,cAAc,CAAC+F,kBAAkB,EAAEpD,UAAU,CAACsD,IAAI,EAAE,IAAI,CAAClF,aAAa,CAAC8D,qBAAqB,CAAC,CAAC,CAAC;IACtH,IAAIqB,mBAAmB,GAAGxG,uBAAuB,CAACiD,UAAU,CAACS,MAAM,CAACE,SAAS,EAAE0C,cAAc,CAAC;IAC9F;AACR;AACA;AACA;IACQ,IAAIF,wBAAwB,EAAE;MAC1B,MAAMK,eAAe,GAAGL,wBAAwB,CAAC5F,uBAAuB,CAACgG,mBAAmB,CAAC,CAAC;MAC9F,IAAI,CAAC3E,qBAAqB,GAAG,CAAC,CAAC4E,eAAe;MAC9C,IAAIA,eAAe,EAAE;QACjBD,mBAAmB,GAAG/F,uBAAuB,CAACgG,eAAe,CAAC;MAClE;IACJ;IACA,OAAOD,mBAAmB;EAC9B;EACAjB,cAAcA,CAACD,QAAQ,EAAE;IACrB,MAAM;MAAEzC,IAAI;MAAE6D,YAAY;MAAEV,WAAW;MAAEW,cAAc;MAAErE,gBAAgB;MAAEsE;IAAqB,CAAC,GAAG,IAAI,CAACrE,QAAQ,CAAC,CAAC;IACnH,MAAMX,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC;IAC1C,MAAMiF,kBAAkB,GAAGxG,QAAQ,CAAEgD,IAAI,IAAK;MAC1C,IAAI,CAACsC,UAAU,CAACtC,IAAI,EAAER,IAAI,EAAE,IAAI,CAACrB,gBAAgB,CAAC,EAAE;QAChD;MACJ;MACA,IAAIsF,UAAU,GAAIlF,WAAW,IAAIA,WAAW,CAACyB,IAAI,CAAC,IAAK,CAAC,CAAC;MACzD,IAAIf,gBAAgB,EAChBwE,UAAU,GAAG;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MACnC;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMC,eAAe,GAAGjB,WAAW,GAAG,GAAG,GAAG,OAAO;MACnD,MAAMkB,aAAa,GAAGlB,WAAW,GAAG,EAAE,GAAG,QAAQ;MACjD,MAAMmB,OAAO,GAAG;QACZC,IAAI,EAAE,SAAS;QACf9B,QAAQ,EAAEoB,YAAY,GAAGpB,QAAQ,CAACjC,IAAI,CAAC,GAAG,CAAC;QAC3C4D,eAAe;QACfC,aAAa;QACbG,YAAY,EAAE,GAAG;QACjBC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE,EAAE;QACb,GAAGZ,cAAc;QACjB,GAAGG;MACP,CAAC;MACD;MACA;MACA;MACA,OAAO,IAAI,CAACU,uBAAuB,CAACnE,IAAI,EAAE8D,OAAO,CAAC;IACtD,CAAC,CAAC;IACF;IACA,OAAOM,OAAO,CAACC,GAAG,CAACb,kBAAkB,CAAC,CAACc,IAAI,CAACf,mBAAmB,CAAC;EACpE;EACAY,uBAAuBA,CAACnE,IAAI,EAAEyD,UAAU,EAAE;IACtC,MAAMlB,SAAS,GAAG,IAAI,CAACrC,kBAAkB,CAACF,IAAI,CAAC;IAC/C,OAAOuC,SAAS,CAAC7D,KAAK,CAACjB,kBAAkB,CAACuC,IAAI,EAAEuC,SAAS,EAAE,CAAC,EAAEkB,UAAU,CAAC,CAAC;EAC9E;EACArE,aAAaA,CAAA,EAAG;IACZpC,QAAQ,CAAEgD,IAAI,IAAK,IAAI,CAACE,kBAAkB,CAACF,IAAI,CAAC,CAACsB,IAAI,CAAC,CAAC,CAAC;EAC5D;EACAnC,cAAcA,CAAA,EAAG;IACbnC,QAAQ,CAAEgD,IAAI,IAAK;MAAE,IAAIwB,EAAE;MAAE,OAAO,CAACA,EAAE,GAAG,IAAI,CAACtB,kBAAkB,CAACF,IAAI,CAAC,CAAC0B,SAAS,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+C,KAAK,CAAC,CAAC;IAAE,CAAC,CAAC;EAC1I;EACA9C,iBAAiBA,CAACzB,IAAI,EAAE;IACpB,IAAIwB,EAAE;IACN,OAAO,CAACA,EAAE,GAAG,IAAI,CAACtB,kBAAkB,CAACF,IAAI,CAAC,CAAC0B,SAAS,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgD,KAAK;EACvG;EACA;AACJ;AACA;AACA;AACA;AACA;EACItE,kBAAkBA,CAACF,IAAI,EAAE;IACrB,MAAMyE,OAAO,GAAG,OAAO,GAAGzE,IAAI,CAAC0E,WAAW,CAAC,CAAC;IAC5C,MAAMC,KAAK,GAAG,IAAI,CAAC3G,aAAa,CAACkB,QAAQ,CAAC,CAAC;IAC3C,MAAM0F,mBAAmB,GAAGD,KAAK,CAACF,OAAO,CAAC;IAC1C,OAAOG,mBAAmB,GACpBA,mBAAmB,GACnB,IAAI,CAAC5G,aAAa,CAAC6G,QAAQ,CAAC7E,IAAI,EAAE,CAAC2E,KAAK,CAACG,OAAO,GAAGH,KAAK,CAACG,OAAO,CAAC9E,IAAI,CAAC,GAAGD,SAAS,KAAK,CAAC,CAAC;EACnG;EACAnB,YAAYA,CAACS,KAAK,EAAE;IAChBrC,QAAQ,CAAEgD,IAAI,IAAK;MACf,MAAM;QAAER;MAAK,CAAC,GAAG,IAAI,CAACN,QAAQ,CAAC,CAAC;MAChC;MACA,IAAI,CAACoD,UAAU,CAACtC,IAAI,EAAER,IAAI,EAAE,IAAI,CAACrB,gBAAgB,CAAC,EAC9C;MACJ,MAAM;QAAEyB;MAAW,CAAC,GAAG,IAAI,CAAC5B,aAAa;MACzC,MAAMuE,SAAS,GAAG,IAAI,CAACrC,kBAAkB,CAACF,IAAI,CAAC;MAC/C,IAAIJ,UAAU,IAAIA,UAAU,CAACS,MAAM,EAAE;QACjC,MAAM;UAAEqD,GAAG;UAAEC;QAAI,CAAC,GAAG/D,UAAU,CAACS,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC;QACtDuC,SAAS,CAACE,GAAG,CAACpD,KAAK,CAACW,IAAI,CAAC,GAAGzC,GAAG,CAACmG,GAAG,EAAEC,GAAG,EAAE,GAAG,CAAC,CAAC;MACnD;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIoB,8BAA8BA,CAAA,EAAG;IAC7B,IAAI,CAAC,IAAI,CAAC/G,aAAa,CAACiC,OAAO,EAC3B;IACJ,MAAM;MAAET,IAAI;MAAEkD;IAAgB,CAAC,GAAG,IAAI,CAACxD,QAAQ,CAAC,CAAC;IACjD,MAAM;MAAEU;IAAW,CAAC,GAAG,IAAI,CAAC5B,aAAa;IACzC,IAAI,CAAC1B,WAAW,CAACoG,eAAe,CAAC,IAAI,CAAC9C,UAAU,IAAI,CAAC,IAAI,CAACrB,WAAW,EACjE;IACJ;AACR;AACA;AACA;IACQ,IAAI,CAACa,aAAa,CAAC,CAAC;IACpB;AACR;AACA;AACA;IACQ,MAAM4F,WAAW,GAAG;MAAE3G,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAClCtB,QAAQ,CAAEgD,IAAI,IAAK;MACf,MAAMuC,SAAS,GAAG,IAAI,CAACrC,kBAAkB,CAACF,IAAI,CAAC;MAC/C,IAAIuC,SAAS,EAAE;QACX,MAAM0C,MAAM,GAAG1C,SAAS,CAACpC,GAAG,CAAC,CAAC;QAC9B6E,WAAW,CAAChF,IAAI,CAAC,GAAGlD,UAAU,CAAC;UAAE4G,GAAG,EAAEuB,MAAM;UAAEtB,GAAG,EAAEsB;QAAO,CAAC,EAAE,IAAI,CAAC1G,WAAW,CAACyB,IAAI,CAAC,CAAC;MACxF;IACJ,CAAC,CAAC;IACF;AACR;AACA;IACQ,MAAM;MAAEkF;IAAkB,CAAC,GAAG,IAAI,CAAClH,aAAa,CAACkB,QAAQ,CAAC,CAAC;IAC3D,IAAI,CAAClB,aAAa,CAACiC,OAAO,CAACkF,KAAK,CAACC,SAAS,GAAGF,iBAAiB,GACxDA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;IACZtF,UAAU,CAACsD,IAAI,IAAItD,UAAU,CAACsD,IAAI,CAACmC,YAAY,CAAC,CAAC;IACjDzF,UAAU,CAAC0F,YAAY,CAAC,CAAC;IACzB,IAAI,CAAC3F,kBAAkB,CAAC,CAAC;IACzB;AACR;AACA;AACA;IACQ3C,QAAQ,CAAEgD,IAAI,IAAK;MACf,IAAI,CAACsC,UAAU,CAACtC,IAAI,EAAER,IAAI,EAAE,IAAI,CAAC,EAC7B;MACJ;AACZ;AACA;MACY,MAAM+C,SAAS,GAAG,IAAI,CAACrC,kBAAkB,CAACF,IAAI,CAAC;MAC/C,MAAM;QAAE0D,GAAG;QAAEC;MAAI,CAAC,GAAG,IAAI,CAACpF,WAAW,CAACyB,IAAI,CAAC;MAC3CuC,SAAS,CAACE,GAAG,CAAClF,GAAG,CAACmG,GAAG,EAAEC,GAAG,EAAEqB,WAAW,CAAChF,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;EACN;EACAuF,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACvH,aAAa,CAACiC,OAAO,EAC3B;IACJrC,mBAAmB,CAAC6E,GAAG,CAAC,IAAI,CAACzE,aAAa,EAAE,IAAI,CAAC;IACjD,MAAMwH,OAAO,GAAG,IAAI,CAACxH,aAAa,CAACiC,OAAO;IAC1C;AACR;AACA;IACQ,MAAMwF,mBAAmB,GAAGlJ,eAAe,CAACiJ,OAAO,EAAE,aAAa,EAAGxG,KAAK,IAAK;MAC3E,MAAM;QAAEQ,IAAI;QAAEkG,YAAY,GAAG;MAAK,CAAC,GAAG,IAAI,CAACxG,QAAQ,CAAC,CAAC;MACrDM,IAAI,IAAIkG,YAAY,IAAI,IAAI,CAAChH,KAAK,CAACM,KAAK,CAAC;IAC7C,CAAC,CAAC;IACF,MAAM2G,sBAAsB,GAAGA,CAAA,KAAM;MACjC,MAAM;QAAEjD;MAAgB,CAAC,GAAG,IAAI,CAACxD,QAAQ,CAAC,CAAC;MAC3C,IAAI5C,WAAW,CAACoG,eAAe,CAAC,EAAE;QAC9B,IAAI,CAACnE,WAAW,GAAG,IAAI,CAACuE,qBAAqB,CAAC,CAAC;MACnD;IACJ,CAAC;IACD,MAAM;MAAElD;IAAW,CAAC,GAAG,IAAI,CAAC5B,aAAa;IACzC,MAAM4H,yBAAyB,GAAGhG,UAAU,CAACiG,gBAAgB,CAAC,SAAS,EAAEF,sBAAsB,CAAC;IAChG,IAAI/F,UAAU,IAAI,CAACA,UAAU,CAACS,MAAM,EAAE;MAClCT,UAAU,CAACsD,IAAI,IAAItD,UAAU,CAACsD,IAAI,CAACmC,YAAY,CAAC,CAAC;MACjDzF,UAAU,CAAC0F,YAAY,CAAC,CAAC;IAC7B;IACAK,sBAAsB,CAAC,CAAC;IACxB;AACR;AACA;AACA;IACQ,MAAMG,kBAAkB,GAAGzI,WAAW,CAAC0I,MAAM,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAChB,8BAA8B,CAAC,CAAC,CAAC;IACrG;AACR;AACA;AACA;IACQ,MAAMiB,wBAAwB,GAAGpG,UAAU,CAACiG,gBAAgB,CAAC,WAAW,EAAG,CAAC;MAAEI,KAAK;MAAEC;IAAiB,CAAC,KAAK;MACxG,IAAI,IAAI,CAAChI,UAAU,IAAIgI,gBAAgB,EAAE;QACrClJ,QAAQ,CAAEgD,IAAI,IAAK;UACf,MAAMmG,WAAW,GAAG,IAAI,CAACjG,kBAAkB,CAACF,IAAI,CAAC;UACjD,IAAI,CAACmG,WAAW,EACZ;UACJ,IAAI,CAAC/H,WAAW,CAAC4B,IAAI,CAAC,IAAIiG,KAAK,CAACjG,IAAI,CAAC,CAACoG,SAAS;UAC/CD,WAAW,CAAC1D,GAAG,CAAC0D,WAAW,CAAChG,GAAG,CAAC,CAAC,GAAG8F,KAAK,CAACjG,IAAI,CAAC,CAACoG,SAAS,CAAC;QAC9D,CAAC,CAAC;QACF,IAAI,CAACpI,aAAa,CAACoD,MAAM,CAAC,CAAC;MAC/B;IACJ,CAAE,CAAC;IACH,OAAO,MAAM;MACT0E,kBAAkB,CAAC,CAAC;MACpBL,mBAAmB,CAAC,CAAC;MACrBG,yBAAyB,CAAC,CAAC;MAC3BI,wBAAwB,IAAIA,wBAAwB,CAAC,CAAC;IAC1D,CAAC;EACL;EACA9G,QAAQA,CAAA,EAAG;IACP,MAAMyF,KAAK,GAAG,IAAI,CAAC3G,aAAa,CAACkB,QAAQ,CAAC,CAAC;IAC3C,MAAM;MAAEM,IAAI,GAAG,KAAK;MAAEsB,iBAAiB,GAAG,KAAK;MAAErB,eAAe,GAAG,KAAK;MAAEiD,eAAe,GAAG,KAAK;MAAEC,WAAW,GAAG/F,cAAc;MAAEyG,YAAY,GAAG;IAAM,CAAC,GAAGsB,KAAK;IAC/J,OAAO;MACH,GAAGA,KAAK;MACRnF,IAAI;MACJsB,iBAAiB;MACjBrB,eAAe;MACfiD,eAAe;MACfC,WAAW;MACXU;IACJ,CAAC;EACL;AACJ;AACA,SAASf,UAAUA,CAAC+D,SAAS,EAAE7G,IAAI,EAAErB,gBAAgB,EAAE;EACnD,OAAQ,CAACqB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK6G,SAAS,MACvClI,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAKkI,SAAS,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASnF,mBAAmBA,CAACD,MAAM,EAAEqF,aAAa,GAAG,EAAE,EAAE;EACrD,IAAID,SAAS,GAAG,IAAI;EACpB,IAAIE,IAAI,CAACC,GAAG,CAACvF,MAAM,CAAC3C,CAAC,CAAC,GAAGgI,aAAa,EAAE;IACpCD,SAAS,GAAG,GAAG;EACnB,CAAC,MACI,IAAIE,IAAI,CAACC,GAAG,CAACvF,MAAM,CAAC5C,CAAC,CAAC,GAAGiI,aAAa,EAAE;IACzCD,SAAS,GAAG,GAAG;EACnB;EACA,OAAOA,SAAS;AACpB;AAEA,SAASvI,yBAAyB,EAAEF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}