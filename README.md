# Gaming Lounge Reservation System

A full-stack web application for managing reservations in a gaming lounge with 12 high-performance gaming PCs.

## Features

- **Futuristic Cyberpunk UI**: Dark theme with neon blue, purple, and green accents
- **Real-time PC Status**: Live updates using Socket.io
- **Reservation System**: Interactive calendar/time picker for booking PCs
- **Admin Dashboard**: Secure admin interface for managing reservations and PC status
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Gamified UX**: Hover effects, animations, and sound effects

## Tech Stack

- **Frontend**: React.js with modern UI components
- **Backend**: Node.js with Express.js
- **Database**: MySQL
- **Real-time**: Socket.io
- **Authentication**: JWT tokens

## Project Structure

```
gaming-lounge/
├── frontend/          # React.js application
├── backend/           # Node.js/Express API
├── database/          # MySQL schema and scripts
└── README.md
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- MySQL (v8.0 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install backend dependencies: `cd backend && npm install`
3. Install frontend dependencies: `cd frontend && npm install`
4. Set up MySQL database using scripts in `/database`
5. Configure environment variables
6. Start the development servers

### Development

- Backend: `cd backend && npm run dev`
- Frontend: `cd frontend && npm start`
- Database: Import schema from `/database/schema.sql`

## API Endpoints

- `GET /api/pcs` - Get all PCs and their status
- `POST /api/reservations` - Create a new reservation
- `GET /api/reservations` - Get all reservations
- `POST /api/auth/login` - Admin login
- `PUT /api/pcs/:id/status` - Update PC status (admin only)

## Real-time Events

- `pc-status-update` - Broadcast PC status changes
- `new-reservation` - Notify about new reservations
- `reservation-cancelled` - Notify about cancelled reservations

## License

MIT License
