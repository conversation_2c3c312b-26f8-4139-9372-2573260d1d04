# Gaming Lounge Reservation System - Setup Guide

## Prerequisites

Before setting up the Gaming Lounge Reservation System, ensure you have the following installed:

- **Node.js** (v16 or higher) - [Download here](https://nodejs.org/)
- **MySQL** (v8.0 or higher) - [Download here](https://dev.mysql.com/downloads/)
- **npm** or **yarn** package manager

## Quick Setup

### 1. Database Setup

1. **Start MySQL server** and create a new database:
   ```sql
   CREATE DATABASE gaming_lounge;
   ```

2. **Import the database schema**:
   ```bash
   mysql -u root -p gaming_lounge < database/schema.sql
   ```

3. **Import initial data**:
   ```bash
   mysql -u root -p gaming_lounge < database/initial_data.sql
   ```

### 2. Backend Setup

1. **Navigate to backend directory**:
   ```bash
   cd backend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Create environment file**:
   ```bash
   cp .env.example .env
   ```

4. **Configure environment variables** in `.env`:
   ```env
   PORT=5000
   NODE_ENV=development
   
   # Database Configuration
   DB_HOST=localhost
   DB_PORT=3306
   DB_NAME=gaming_lounge
   DB_USER=root
   DB_PASSWORD=your_mysql_password
   
   # JWT Configuration
   JWT_SECRET=your_super_secret_jwt_key_here
   JWT_EXPIRES_IN=24h
   
   # Admin Configuration
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=admin123
   
   # CORS Configuration
   FRONTEND_URL=http://localhost:3000
   ```

5. **Start the backend server**:
   ```bash
   npm run dev
   ```

   The backend will be running on `http://localhost:5000`

### 3. Frontend Setup

1. **Open a new terminal** and navigate to frontend directory:
   ```bash
   cd frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Create environment file**:
   ```bash
   cp .env.example .env
   ```

4. **Configure environment variables** in `.env`:
   ```env
   REACT_APP_API_URL=http://localhost:5000
   REACT_APP_SOCKET_URL=http://localhost:5000
   REACT_APP_NAME=Gaming Lounge
   REACT_APP_VERSION=1.0.0
   ```

5. **Start the frontend development server**:
   ```bash
   npm start
   ```

   The frontend will be running on `http://localhost:3000`

## Default Admin Credentials

- **Email**: `<EMAIL>`
- **Password**: `admin123`

**⚠️ Important**: Change these credentials in production!

## Features Overview

### Customer Features
- **PC Grid View**: See all 12 gaming PCs with real-time status
- **Reservation System**: Book PCs with date/time selection
- **Real-time Updates**: Live status updates via WebSocket
- **Responsive Design**: Works on desktop, tablet, and mobile

### Admin Features
- **Dashboard**: Overview of statistics and recent reservations
- **PC Management**: Update PC status (available, maintenance, etc.)
- **Reservation Management**: View and manage all reservations
- **Real-time Monitoring**: Live updates of all system activity

### Technical Features
- **Real-time Communication**: Socket.io for live updates
- **Cyberpunk UI**: Futuristic dark theme with neon accents
- **Sound Effects**: Interactive audio feedback
- **Animations**: Smooth transitions and hover effects
- **Security**: JWT authentication and input validation

## Troubleshooting

### Common Issues

1. **Database Connection Error**:
   - Ensure MySQL is running
   - Check database credentials in `.env`
   - Verify database exists and schema is imported

2. **Port Already in Use**:
   - Change PORT in backend `.env` file
   - Update REACT_APP_API_URL in frontend `.env`

3. **CORS Errors**:
   - Ensure FRONTEND_URL in backend `.env` matches frontend URL
   - Check that both servers are running

4. **Socket Connection Issues**:
   - Verify REACT_APP_SOCKET_URL matches backend URL
   - Check firewall settings

### Development Commands

**Backend**:
- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server
- `npm test` - Run tests

**Frontend**:
- `npm start` - Start development server
- `npm run build` - Build for production
- `npm test` - Run tests

## Production Deployment

### Environment Variables for Production

**Backend**:
```env
NODE_ENV=production
PORT=5000
DB_HOST=your_production_db_host
DB_PASSWORD=secure_password
JWT_SECRET=very_secure_random_string
FRONTEND_URL=https://your-domain.com
```

**Frontend**:
```env
REACT_APP_API_URL=https://api.your-domain.com
REACT_APP_SOCKET_URL=https://api.your-domain.com
```

### Security Checklist

- [ ] Change default admin credentials
- [ ] Use strong JWT secret
- [ ] Enable HTTPS in production
- [ ] Configure proper CORS origins
- [ ] Set up database backups
- [ ] Configure rate limiting
- [ ] Enable logging and monitoring

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the console logs for error messages
3. Ensure all dependencies are properly installed
4. Verify environment variables are correctly set

## License

MIT License - see LICENSE file for details.
