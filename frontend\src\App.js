import React from 'react';
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import styled from 'styled-components';

// Context Providers
import { AuthProvider } from './contexts/AuthContext';
import { SocketProvider } from './contexts/SocketContext';

// Components
import Header from './components/Layout/Header';
import Footer from './components/Layout/Footer';
import ProtectedRoute from './components/Auth/ProtectedRoute';

// Pages
import Home from './pages/Home';
import AdminLogin from './pages/AdminLogin';
import AdminDashboard from './pages/AdminDashboard';
import ReservationForm from './pages/ReservationForm';

// Global Styles
import GlobalStyles from './styles/GlobalStyles';

const AppContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
  font-family: 'Raj<PERSON><PERSON>', sans-serif;
  position: relative;
  overflow-x: hidden;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
      radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(0, 255, 0, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }
`;

const MainContent = styled.main`
  min-height: calc(100vh - 140px);
  padding-top: 80px;
`;

const CyberpunkGrid = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
  z-index: -1;
  animation: gridMove 20s linear infinite;

  @keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
  }
`;

function App() {
  return (
    <Router>
      <AuthProvider>
        <SocketProvider>
          <GlobalStyles />
          <AppContainer>
            <CyberpunkGrid />
            <Header />
            <MainContent>
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/reserve/:pcId" element={<ReservationForm />} />
                <Route path="/admin/login" element={<AdminLogin />} />
                <Route 
                  path="/admin/dashboard" 
                  element={
                    <ProtectedRoute requireAdmin>
                      <AdminDashboard />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/admin/*" 
                  element={
                    <ProtectedRoute requireAdmin>
                      <AdminDashboard />
                    </ProtectedRoute>
                  } 
                />
              </Routes>
            </MainContent>
            <Footer />
            
            {/* Toast notifications with cyberpunk styling */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: 'rgba(26, 26, 46, 0.95)',
                  color: '#00ffff',
                  border: '1px solid #00ffff',
                  borderRadius: '8px',
                  fontFamily: 'Rajdhani, sans-serif',
                  fontSize: '16px',
                  fontWeight: '500',
                  backdropFilter: 'blur(10px)',
                  boxShadow: '0 0 20px rgba(0, 255, 255, 0.3)',
                },
                success: {
                  style: {
                    border: '1px solid #00ff00',
                    color: '#00ff00',
                    boxShadow: '0 0 20px rgba(0, 255, 0, 0.3)',
                  },
                },
                error: {
                  style: {
                    border: '1px solid #ff0040',
                    color: '#ff0040',
                    boxShadow: '0 0 20px rgba(255, 0, 64, 0.3)',
                  },
                },
              }}
            />
          </AppContainer>
        </SocketProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
