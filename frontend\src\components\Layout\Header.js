import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';

const HeaderContainer = styled(motion.header)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  padding: 1rem 2rem;
  
  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const HeaderContent = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Logo = styled(Link)`
  font-family: 'Orbitron', monospace;
  font-size: 1.8rem;
  font-weight: 900;
  background: linear-gradient(45deg, #00ffff, #8a2be2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.2);
  }

  @media (max-width: 768px) {
    font-size: 1.4rem;
  }
`;

const Nav = styled.nav`
  display: flex;
  align-items: center;
  gap: 2rem;

  @media (max-width: 768px) {
    gap: 1rem;
  }
`;

const NavLink = styled(Link)`
  color: #ffffff;
  text-decoration: none;
  font-weight: 500;
  font-size: 1.1rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    color: #00ffff;
    background: rgba(0, 255, 255, 0.1);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
  }

  &.active {
    color: #00ffff;
    background: rgba(0, 255, 255, 0.2);
  }

  @media (max-width: 768px) {
    font-size: 1rem;
    padding: 0.4rem 0.8rem;
  }
`;

const LiveClock = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: 'Orbitron', monospace;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);

  .time {
    font-size: 1.2rem;
    font-weight: 700;
  }

  .date {
    font-size: 0.8rem;
    opacity: 0.8;
  }

  @media (max-width: 768px) {
    .time {
      font-size: 1rem;
    }
    .date {
      font-size: 0.7rem;
    }
  }
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: ${props => props.connected ? '#00ff00' : '#ff0040'};

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: ${props => props.connected ? 'pulse 2s infinite' : 'none'};
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
`;

const UserMenu = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 0.9rem;

  .username {
    color: #00ffff;
    font-weight: 600;
  }

  .role {
    color: #8a2be2;
    font-size: 0.8rem;
  }

  @media (max-width: 768px) {
    display: none;
  }
`;

const LogoutButton = styled.button`
  background: linear-gradient(45deg, #ff0040, #ff6600);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 0, 64, 0.4);
  }

  @media (max-width: 768px) {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
`;

const Header = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const { isAuthenticated, isAdmin, user, logout } = useAuth();
  const { connected } = useSocket();
  const navigate = useNavigate();

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <HeaderContainer
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <HeaderContent>
        <Logo to="/">CYBERZONE</Logo>
        
        <Nav>
          <LiveClock>
            <div className="time">{formatTime(currentTime)}</div>
            <div className="date">{formatDate(currentTime)}</div>
          </LiveClock>

          <StatusIndicator connected={connected}>
            <div className="dot"></div>
            {connected ? 'ONLINE' : 'OFFLINE'}
          </StatusIndicator>

          {isAuthenticated ? (
            <UserMenu>
              {isAdmin && (
                <NavLink to="/admin/dashboard">Dashboard</NavLink>
              )}
              
              <UserInfo>
                <div className="username">{user?.username || user?.email}</div>
                <div className="role">{isAdmin ? 'ADMIN' : 'USER'}</div>
              </UserInfo>
              
              <LogoutButton onClick={handleLogout}>
                LOGOUT
              </LogoutButton>
            </UserMenu>
          ) : (
            <NavLink to="/admin/login">Admin Login</NavLink>
          )}
        </Nav>
      </HeaderContent>
    </HeaderContainer>
  );
};

export default Header;
