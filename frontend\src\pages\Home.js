import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import axios from 'axios';
import toast from 'react-hot-toast';
import { useSocket } from '../contexts/SocketContext';
import PCCard from '../components/PC/PCCard';
import LoadingSpinner from '../components/UI/LoadingSpinner';

const HomeContainer = styled.div`
  min-height: 100vh;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const HeroSection = styled(motion.section)`
  text-align: center;
  margin-bottom: 4rem;
  padding: 3rem 0;
`;

const HeroTitle = styled(motion.h1)`
  font-size: 4rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #00ffff, #8a2be2, #00ff00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: neonFlicker 3s ease-in-out infinite;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled(motion.p)`
  font-size: 1.5rem;
  color: #b0b0b0;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 1.2rem;
  }
`;

const StatsBar = styled(motion.div)`
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    gap: 1.5rem;
  }
`;

const StatItem = styled.div`
  text-align: center;
  padding: 1rem;
  background: rgba(26, 26, 46, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 12px;
  min-width: 120px;

  .number {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    font-weight: 700;
    color: #00ffff;
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
  }

  .label {
    font-size: 0.9rem;
    color: #b0b0b0;
    margin-top: 0.5rem;
  }
`;

const PCGridSection = styled.section`
  margin-bottom: 4rem;
`;

const SectionTitle = styled(motion.h2)`
  text-align: center;
  margin-bottom: 3rem;
  color: #00ffff;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
`;

const PCGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }
`;

const FilterBar = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
`;

const FilterButton = styled.button`
  padding: 0.8rem 1.5rem;
  background: ${props => props.active 
    ? 'linear-gradient(45deg, #00ffff, #8a2be2)' 
    : 'rgba(26, 26, 46, 0.3)'};
  color: white;
  border: 1px solid ${props => props.active ? 'transparent' : 'rgba(0, 255, 255, 0.3)'};
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3);
  }
`;

const Home = () => {
  const [pcs, setPcs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const { pcStatuses, connected } = useSocket();

  useEffect(() => {
    fetchPCs();
  }, []);

  const fetchPCs = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/pcs');
      setPcs(response.data);
    } catch (error) {
      console.error('Error fetching PCs:', error);
      toast.error('Failed to load gaming PCs');
    } finally {
      setLoading(false);
    }
  };

  // Update PC statuses from socket
  useEffect(() => {
    if (Object.keys(pcStatuses).length > 0) {
      setPcs(prevPcs => 
        prevPcs.map(pc => ({
          ...pc,
          current_status: pcStatuses[pc.id] || pc.current_status
        }))
      );
    }
  }, [pcStatuses]);

  const getFilteredPCs = () => {
    if (filter === 'all') return pcs;
    return pcs.filter(pc => pc.current_status === filter);
  };

  const getStats = () => {
    const total = pcs.length;
    const available = pcs.filter(pc => pc.current_status === 'available').length;
    const inUse = pcs.filter(pc => pc.current_status === 'in_use').length;
    const reserved = pcs.filter(pc => pc.current_status === 'reserved').length;
    const maintenance = pcs.filter(pc => pc.current_status === 'maintenance').length;

    return { total, available, inUse, reserved, maintenance };
  };

  const stats = getStats();
  const filteredPCs = getFilteredPCs();

  if (loading) {
    return (
      <HomeContainer>
        <LoadingSpinner />
      </HomeContainer>
    );
  }

  return (
    <HomeContainer>
      <HeroSection
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <HeroTitle
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 0.2 }}
        >
          WELCOME TO CYBERZONE
        </HeroTitle>
        <HeroSubtitle
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          Experience gaming like never before with our high-performance gaming rigs
        </HeroSubtitle>
      </HeroSection>

      <StatsBar
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.6 }}
      >
        <StatItem>
          <div className="number">{stats.total}</div>
          <div className="label">Total PCs</div>
        </StatItem>
        <StatItem>
          <div className="number">{stats.available}</div>
          <div className="label">Available</div>
        </StatItem>
        <StatItem>
          <div className="number">{stats.inUse}</div>
          <div className="label">In Use</div>
        </StatItem>
        <StatItem>
          <div className="number">{stats.reserved}</div>
          <div className="label">Reserved</div>
        </StatItem>
      </StatsBar>

      <PCGridSection>
        <SectionTitle
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          GAMING STATIONS
        </SectionTitle>

        <FilterBar>
          <FilterButton 
            active={filter === 'all'} 
            onClick={() => setFilter('all')}
          >
            All ({pcs.length})
          </FilterButton>
          <FilterButton 
            active={filter === 'available'} 
            onClick={() => setFilter('available')}
          >
            Available ({stats.available})
          </FilterButton>
          <FilterButton 
            active={filter === 'in_use'} 
            onClick={() => setFilter('in_use')}
          >
            In Use ({stats.inUse})
          </FilterButton>
          <FilterButton 
            active={filter === 'reserved'} 
            onClick={() => setFilter('reserved')}
          >
            Reserved ({stats.reserved})
          </FilterButton>
        </FilterBar>

        <PCGrid
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1 }}
        >
          {filteredPCs.map((pc, index) => (
            <PCCard 
              key={pc.id} 
              pc={pc} 
              index={index}
              connected={connected}
            />
          ))}
        </PCGrid>
      </PCGridSection>
    </HomeContainer>
  );
};

export default Home;
