{"ast": null, "code": "function getFinalKeyframe(keyframes, {\n  repeat,\n  repeatType = \"loop\"\n}) {\n  const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1 ? 0 : keyframes.length - 1;\n  return keyframes[index];\n}\nexport { getFinalKeyframe };", "map": {"version": 3, "names": ["getFinalKeyframe", "keyframes", "repeat", "repeatType", "index", "length"], "sources": ["C:/Users/<USER>/Desktop/hamza/frontend/node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs"], "sourcesContent": ["function getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }) {\n    const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1\n        ? 0\n        : keyframes.length - 1;\n    return keyframes[index];\n}\n\nexport { getFinalKeyframe };\n"], "mappings": "AAAA,SAASA,gBAAgBA,CAACC,SAAS,EAAE;EAAEC,MAAM;EAAEC,UAAU,GAAG;AAAO,CAAC,EAAE;EAClE,MAAMC,KAAK,GAAGF,MAAM,IAAIC,UAAU,KAAK,MAAM,IAAID,MAAM,GAAG,CAAC,KAAK,CAAC,GAC3D,CAAC,GACDD,SAAS,CAACI,MAAM,GAAG,CAAC;EAC1B,OAAOJ,SAAS,CAACG,KAAK,CAAC;AAC3B;AAEA,SAASJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}